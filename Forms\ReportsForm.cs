using System;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using MobileRepairShop.DataAccess;
using MobileRepairShop.Models;

namespace MobileRepairShop.Forms
{
    public partial class ReportsForm : Form
    {
        private readonly RepairOrderRepository _orderRepository;
        private readonly PartsRepository _partsRepository;
        private readonly CustomerRepository _customerRepository;

        private DateTimePicker dtpFromDate;
        private DateTimePicker dtpToDate;
        private ComboBox cmbReportType;
        private DataGridView dgvReport;
        private Button btnGenerateReport;
        private Button btnExportCSV;
        private Button btnExportPDF;
        private Label lblSummary;

        public ReportsForm()
        {
            _orderRepository = new RepairOrderRepository();
            _partsRepository = new PartsRepository();
            _customerRepository = new CustomerRepository();
            
            InitializeComponent();
            SetupReportsForm();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // ReportsForm
            this.AutoScaleDimensions = new SizeF(8F, 16F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.BackColor = Color.FromArgb(240, 240, 240);
            this.ClientSize = new Size(1000, 700);
            this.Name = "ReportsForm";
            this.Text = "Reports";
            
            this.ResumeLayout(false);
        }

        private void SetupReportsForm()
        {
            // Title
            var titleLabel = new Label
            {
                Text = "Reports & Analytics",
                Font = new Font("Segoe UI", 24, FontStyle.Bold),
                ForeColor = Color.FromArgb(51, 51, 76),
                AutoSize = true,
                Location = new Point(20, 20)
            };

            // Filter Panel
            var filterPanel = new Panel
            {
                Location = new Point(20, 70),
                Size = new Size(960, 80),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            // Date Range
            var lblFromDate = new Label
            {
                Text = "From Date:",
                Font = new Font("Segoe UI", 10),
                Location = new Point(10, 15),
                AutoSize = true
            };

            dtpFromDate = new DateTimePicker
            {
                Location = new Point(10, 35),
                Size = new Size(120, 25),
                Font = new Font("Segoe UI", 10),
                Value = DateTime.Now.AddMonths(-1)
            };

            var lblToDate = new Label
            {
                Text = "To Date:",
                Font = new Font("Segoe UI", 10),
                Location = new Point(150, 15),
                AutoSize = true
            };

            dtpToDate = new DateTimePicker
            {
                Location = new Point(150, 35),
                Size = new Size(120, 25),
                Font = new Font("Segoe UI", 10),
                Value = DateTime.Now
            };

            // Report Type
            var lblReportType = new Label
            {
                Text = "Report Type:",
                Font = new Font("Segoe UI", 10),
                Location = new Point(290, 15),
                AutoSize = true
            };

            cmbReportType = new ComboBox
            {
                Location = new Point(290, 35),
                Size = new Size(200, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Segoe UI", 10)
            };
            cmbReportType.Items.AddRange(new[] 
            { 
                "Sales Summary", 
                "Order Details", 
                "Parts Usage", 
                "Customer Report", 
                "Revenue Analysis",
                "Low Stock Report",
                "Technician Performance"
            });
            cmbReportType.SelectedIndex = 0;

            // Buttons
            btnGenerateReport = CreateButton("Generate Report", new Point(510, 30), Color.FromArgb(0, 126, 249));
            btnExportCSV = CreateButton("Export CSV", new Point(630, 30), Color.FromArgb(40, 167, 69));
            btnExportPDF = CreateButton("Export PDF", new Point(750, 30), Color.FromArgb(220, 53, 69));

            btnGenerateReport.Click += BtnGenerateReport_Click;
            btnExportCSV.Click += BtnExportCSV_Click;
            btnExportPDF.Click += BtnExportPDF_Click;

            filterPanel.Controls.AddRange(new Control[]
            {
                lblFromDate, dtpFromDate, lblToDate, dtpToDate,
                lblReportType, cmbReportType, btnGenerateReport, btnExportCSV, btnExportPDF
            });

            // Summary Panel
            var summaryPanel = new Panel
            {
                Location = new Point(20, 170),
                Size = new Size(960, 50),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            lblSummary = new Label
            {
                Text = "Select a report type and click Generate Report to view data.",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(51, 51, 76),
                Location = new Point(10, 15),
                AutoSize = true
            };

            summaryPanel.Controls.Add(lblSummary);

            // Report DataGridView
            dgvReport = new DataGridView
            {
                Location = new Point(20, 240),
                Size = new Size(960, 440),
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };

            StyleDataGridView(dgvReport);

            // Add controls to form
            this.Controls.Add(titleLabel);
            this.Controls.Add(filterPanel);
            this.Controls.Add(summaryPanel);
            this.Controls.Add(dgvReport);
        }

        private Button CreateButton(string text, Point location, Color backColor)
        {
            return new Button
            {
                Text = text,
                Location = location,
                Size = new Size(110, 30),
                BackColor = backColor,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9),
                Cursor = Cursors.Hand,
                FlatAppearance = { BorderSize = 0 }
            };
        }

        private void StyleDataGridView(DataGridView dgv)
        {
            dgv.EnableHeadersVisualStyles = false;
            dgv.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(51, 51, 76);
            dgv.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgv.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10, FontStyle.Bold);
            dgv.DefaultCellStyle.SelectionBackColor = Color.FromArgb(0, 126, 249);
            dgv.DefaultCellStyle.SelectionForeColor = Color.White;
            dgv.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);
            dgv.RowHeadersVisible = false;
        }

        private void BtnGenerateReport_Click(object sender, EventArgs e)
        {
            try
            {
                var fromDate = dtpFromDate.Value.Date;
                var toDate = dtpToDate.Value.Date.AddDays(1).AddSeconds(-1);
                var reportType = cmbReportType.SelectedItem.ToString();

                switch (reportType)
                {
                    case "Sales Summary":
                        GenerateSalesSummaryReport(fromDate, toDate);
                        break;
                    case "Order Details":
                        GenerateOrderDetailsReport(fromDate, toDate);
                        break;
                    case "Parts Usage":
                        GeneratePartsUsageReport(fromDate, toDate);
                        break;
                    case "Customer Report":
                        GenerateCustomerReport(fromDate, toDate);
                        break;
                    case "Revenue Analysis":
                        GenerateRevenueAnalysisReport(fromDate, toDate);
                        break;
                    case "Low Stock Report":
                        GenerateLowStockReport();
                        break;
                    case "Technician Performance":
                        GenerateTechnicianPerformanceReport(fromDate, toDate);
                        break;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error generating report: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void GenerateSalesSummaryReport(DateTime fromDate, DateTime toDate)
        {
            var orders = _orderRepository.GetAllOrders()
                .Where(o => o.OrderDate >= fromDate && o.OrderDate <= toDate)
                .ToList();

            var summary = orders.GroupBy(o => o.OrderDate.Date)
                .Select(g => new
                {
                    Date = g.Key.ToString("MM/dd/yyyy"),
                    TotalOrders = g.Count(),
                    CompletedOrders = g.Count(o => o.Status == OrderStatus.Completed),
                    PendingOrders = g.Count(o => o.Status == OrderStatus.Pending),
                    TotalRevenue = g.Where(o => o.Status == OrderStatus.Completed).Sum(o => o.TotalAmount).ToString("C"),
                    AvgOrderValue = g.Where(o => o.Status == OrderStatus.Completed).Any() ? 
                        g.Where(o => o.Status == OrderStatus.Completed).Average(o => o.TotalAmount).ToString("C") : "$0.00"
                })
                .OrderBy(s => s.Date)
                .ToList();

            dgvReport.DataSource = summary;

            var totalRevenue = orders.Where(o => o.Status == OrderStatus.Completed).Sum(o => o.TotalAmount);
            var totalOrders = orders.Count;
            var completedOrders = orders.Count(o => o.Status == OrderStatus.Completed);

            lblSummary.Text = $"Period: {fromDate:MM/dd/yyyy} - {toDate:MM/dd/yyyy} | " +
                             $"Total Orders: {totalOrders} | Completed: {completedOrders} | " +
                             $"Total Revenue: {totalRevenue:C}";
        }

        private void GenerateOrderDetailsReport(DateTime fromDate, DateTime toDate)
        {
            var orders = _orderRepository.GetAllOrders()
                .Where(o => o.OrderDate >= fromDate && o.OrderDate <= toDate)
                .Select(o => new
                {
                    OrderNumber = o.OrderNumber,
                    Date = o.OrderDate.ToString("MM/dd/yyyy"),
                    Customer = o.Customer?.FullName ?? "Unknown",
                    PhoneModel = o.PhoneModel,
                    Status = o.Status.ToString(),
                    Amount = o.TotalAmount.ToString("C"),
                    Technician = o.TechnicianName ?? "Not assigned"
                })
                .OrderByDescending(o => o.Date)
                .ToList();

            dgvReport.DataSource = orders;
            lblSummary.Text = $"Order Details Report | Period: {fromDate:MM/dd/yyyy} - {toDate:MM/dd/yyyy} | Total Orders: {orders.Count}";
        }

        private void GeneratePartsUsageReport(DateTime fromDate, DateTime toDate)
        {
            var orders = _orderRepository.GetAllOrders()
                .Where(o => o.OrderDate >= fromDate && o.OrderDate <= toDate)
                .ToList();

            var partsUsage = orders.SelectMany(o => o.OrderParts)
                .GroupBy(op => op.Part?.PartName ?? "Unknown")
                .Select(g => new
                {
                    PartName = g.Key,
                    TotalQuantity = g.Sum(op => op.Quantity),
                    TotalRevenue = g.Sum(op => op.TotalPrice).ToString("C"),
                    TimesUsed = g.Count(),
                    AvgPrice = g.Average(op => op.UnitPrice).ToString("C")
                })
                .OrderByDescending(p => p.TotalQuantity)
                .ToList();

            dgvReport.DataSource = partsUsage;
            lblSummary.Text = $"Parts Usage Report | Period: {fromDate:MM/dd/yyyy} - {toDate:MM/dd/yyyy} | Unique Parts: {partsUsage.Count}";
        }

        private void GenerateCustomerReport(DateTime fromDate, DateTime toDate)
        {
            var orders = _orderRepository.GetAllOrders()
                .Where(o => o.OrderDate >= fromDate && o.OrderDate <= toDate)
                .ToList();

            var customerReport = orders.GroupBy(o => o.Customer?.CustomerID ?? 0)
                .Where(g => g.Key != 0)
                .Select(g => new
                {
                    Customer = g.First().Customer?.FullName ?? "Unknown",
                    Phone = g.First().Customer?.PhoneNumber ?? "Not provided",
                    TotalOrders = g.Count(),
                    CompletedOrders = g.Count(o => o.Status == OrderStatus.Completed),
                    TotalSpent = g.Where(o => o.Status == OrderStatus.Completed).Sum(o => o.TotalAmount).ToString("C"),
                    LastOrder = g.Max(o => o.OrderDate).ToString("MM/dd/yyyy")
                })
                .OrderByDescending(c => c.TotalOrders)
                .ToList();

            dgvReport.DataSource = customerReport;
            lblSummary.Text = $"Customer Report | Period: {fromDate:MM/dd/yyyy} - {toDate:MM/dd/yyyy} | Active Customers: {customerReport.Count}";
        }

        private void GenerateRevenueAnalysisReport(DateTime fromDate, DateTime toDate)
        {
            var orders = _orderRepository.GetAllOrders()
                .Where(o => o.OrderDate >= fromDate && o.OrderDate <= toDate && o.Status == OrderStatus.Completed)
                .ToList();

            var revenueAnalysis = orders.GroupBy(o => new { o.OrderDate.Year, o.OrderDate.Month })
                .Select(g => new
                {
                    Month = $"{g.Key.Year}-{g.Key.Month:00}",
                    TotalOrders = g.Count(),
                    PartsRevenue = g.SelectMany(o => o.OrderParts).Sum(op => op.TotalPrice).ToString("C"),
                    LaborRevenue = g.Sum(o => o.LaborCost).ToString("C"),
                    TotalRevenue = g.Sum(o => o.TotalAmount).ToString("C"),
                    AvgOrderValue = g.Average(o => o.TotalAmount).ToString("C")
                })
                .OrderBy(r => r.Month)
                .ToList();

            dgvReport.DataSource = revenueAnalysis;
            
            var totalRevenue = orders.Sum(o => o.TotalAmount);
            lblSummary.Text = $"Revenue Analysis | Period: {fromDate:MM/dd/yyyy} - {toDate:MM/dd/yyyy} | Total Revenue: {totalRevenue:C}";
        }

        private void GenerateLowStockReport()
        {
            var lowStockParts = _partsRepository.GetLowStockParts()
                .Select(p => new
                {
                    PartName = p.PartName,
                    PartNumber = p.PartNumber,
                    Category = p.Category,
                    Brand = p.Brand,
                    CurrentStock = p.StockQuantity,
                    MinLevel = p.MinStockLevel,
                    Shortage = p.MinStockLevel - p.StockQuantity,
                    CostPrice = p.CostPrice.ToString("C"),
                    SellPrice = p.SellPrice.ToString("C")
                })
                .OrderBy(p => p.CurrentStock)
                .ToList();

            dgvReport.DataSource = lowStockParts;
            lblSummary.Text = $"Low Stock Report | Parts Below Minimum Level: {lowStockParts.Count}";
        }

        private void GenerateTechnicianPerformanceReport(DateTime fromDate, DateTime toDate)
        {
            var orders = _orderRepository.GetAllOrders()
                .Where(o => o.OrderDate >= fromDate && o.OrderDate <= toDate && !string.IsNullOrEmpty(o.TechnicianName))
                .ToList();

            var techPerformance = orders.GroupBy(o => o.TechnicianName)
                .Select(g => new
                {
                    Technician = g.Key,
                    TotalOrders = g.Count(),
                    CompletedOrders = g.Count(o => o.Status == OrderStatus.Completed),
                    PendingOrders = g.Count(o => o.Status == OrderStatus.Pending),
                    InProgressOrders = g.Count(o => o.Status == OrderStatus.InProgress),
                    CompletionRate = $"{(g.Count(o => o.Status == OrderStatus.Completed) * 100.0 / g.Count()):F1}%",
                    TotalRevenue = g.Where(o => o.Status == OrderStatus.Completed).Sum(o => o.TotalAmount).ToString("C")
                })
                .OrderByDescending(t => t.CompletedOrders)
                .ToList();

            dgvReport.DataSource = techPerformance;
            lblSummary.Text = $"Technician Performance | Period: {fromDate:MM/dd/yyyy} - {toDate:MM/dd/yyyy} | Active Technicians: {techPerformance.Count}";
        }

        private void BtnExportCSV_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvReport.DataSource == null)
                {
                    MessageBox.Show("Please generate a report first.", "No Data", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var saveFileDialog = new SaveFileDialog
                {
                    Filter = "CSV files (*.csv)|*.csv",
                    FileName = $"{cmbReportType.SelectedItem}_{DateTime.Now:yyyyMMdd}.csv"
                };

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    ExportToCSV(saveFileDialog.FileName);
                    MessageBox.Show("Report exported successfully.", "Export Complete", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error exporting to CSV: {ex.Message}", "Export Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnExportPDF_Click(object sender, EventArgs e)
        {
            MessageBox.Show("PDF export functionality would require additional libraries like iTextSharp. " +
                           "For now, please use CSV export or print the report.", 
                           "PDF Export", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ExportToCSV(string fileName)
        {
            var csv = new StringBuilder();
            
            // Add headers
            var headers = dgvReport.Columns.Cast<DataGridViewColumn>().Select(column => column.HeaderText);
            csv.AppendLine(string.Join(",", headers));

            // Add rows
            foreach (DataGridViewRow row in dgvReport.Rows)
            {
                var fields = row.Cells.Cast<DataGridViewCell>().Select(cell => 
                    $"\"{cell.Value?.ToString().Replace("\"", "\"\"")}\"");
                csv.AppendLine(string.Join(",", fields));
            }

            File.WriteAllText(fileName, csv.ToString());
        }
    }
}
