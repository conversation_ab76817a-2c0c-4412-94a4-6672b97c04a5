{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\mobli app\\MobileRepairShop.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\mobli app\\MobileRepairShop.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\mobli app\\MobileRepairShop.csproj", "projectName": "MobileRepairShop", "projectPath": "C:\\Users\\<USER>\\Desktop\\mobli app\\MobileRepairShop.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\mobli app\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net48"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"targetAlias": "net48", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net48": {"targetAlias": "net48", "dependencies": {"Microsoft.NETFramework.ReferenceAssemblies": {"suppressParent": "All", "target": "Package", "version": "[1.0.3, )", "autoReferenced": true}, "System.Configuration.ConfigurationManager": {"target": "Package", "version": "[8.0.0, )"}, "System.Data.SqlClient": {"target": "Package", "version": "[4.8.6, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.305\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win-x86": {"#import": []}}}}}