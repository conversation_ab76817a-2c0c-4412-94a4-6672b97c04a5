using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using MobileRepairShop.DataAccess;
using MobileRepairShop.Models;

namespace MobileRepairShop.Forms
{
    public partial class SelectPartForm : Form
    {
        private readonly PartsRepository _partsRepository;
        private DataGridView dgvParts;
        private TextBox txtSearch;
        private NumericUpDown nudQuantity;
        private CheckBox chkIsReplaced;
        private Button btnSelect;
        private Button btnCancel;

        public Part SelectedPart { get; private set; }
        public int Quantity { get; private set; }
        public bool IsReplaced { get; private set; }

        public SelectPartForm()
        {
            _partsRepository = new PartsRepository();
            InitializeComponent();
            SetupForm();
            LoadParts();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // SelectPartForm
            this.AutoScaleDimensions = new SizeF(8F, 16F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.BackColor = Color.FromArgb(240, 240, 240);
            this.ClientSize = new Size(800, 600);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "SelectPartForm";
            this.StartPosition = FormStartPosition.CenterParent;
            this.Text = "Select Part";
            
            this.ResumeLayout(false);
        }

        private void SetupForm()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20),
                BackColor = Color.White
            };

            // Title
            var titleLabel = new Label
            {
                Text = "Select Part",
                Font = new Font("Segoe UI", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(51, 51, 76),
                AutoSize = true,
                Location = new Point(20, 20)
            };

            // Search
            var lblSearch = new Label
            {
                Text = "Search:",
                Font = new Font("Segoe UI", 10),
                Location = new Point(20, 60),
                AutoSize = true
            };

            txtSearch = new TextBox
            {
                Location = new Point(80, 57),
                Size = new Size(300, 25),
                Font = new Font("Segoe UI", 10)
            };
            txtSearch.TextChanged += TxtSearch_TextChanged;

            // Parts grid
            dgvParts = new DataGridView
            {
                Location = new Point(20, 100),
                Size = new Size(740, 350),
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };
            StyleDataGridView(dgvParts);

            // Quantity
            var lblQuantity = new Label
            {
                Text = "Quantity:",
                Font = new Font("Segoe UI", 10),
                Location = new Point(20, 470),
                AutoSize = true
            };

            nudQuantity = new NumericUpDown
            {
                Location = new Point(90, 467),
                Size = new Size(80, 25),
                Font = new Font("Segoe UI", 10),
                Minimum = 1,
                Maximum = 100,
                Value = 1
            };

            // Is Replaced checkbox
            chkIsReplaced = new CheckBox
            {
                Text = "Replaced (uncheck if repaired)",
                Location = new Point(200, 470),
                AutoSize = true,
                Font = new Font("Segoe UI", 10),
                Checked = true
            };

            // Buttons
            btnSelect = new Button
            {
                Text = "Select Part",
                Location = new Point(580, 520),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10),
                Cursor = Cursors.Hand,
                FlatAppearance = { BorderSize = 0 }
            };
            btnSelect.Click += BtnSelect_Click;

            btnCancel = new Button
            {
                Text = "Cancel",
                Location = new Point(690, 520),
                Size = new Size(80, 35),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10),
                Cursor = Cursors.Hand,
                FlatAppearance = { BorderSize = 0 }
            };
            btnCancel.Click += (s, e) => this.DialogResult = DialogResult.Cancel;

            // Add controls to panel
            panel.Controls.AddRange(new Control[]
            {
                titleLabel, lblSearch, txtSearch, dgvParts,
                lblQuantity, nudQuantity, chkIsReplaced,
                btnSelect, btnCancel
            });

            this.Controls.Add(panel);
        }

        private void StyleDataGridView(DataGridView dgv)
        {
            dgv.EnableHeadersVisualStyles = false;
            dgv.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(51, 51, 76);
            dgv.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgv.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10, FontStyle.Bold);
            dgv.DefaultCellStyle.SelectionBackColor = Color.FromArgb(0, 126, 249);
            dgv.DefaultCellStyle.SelectionForeColor = Color.White;
            dgv.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);
            dgv.RowHeadersVisible = false;
        }

        private void LoadParts()
        {
            try
            {
                var parts = _partsRepository.GetAllParts().Select(p => new
                {
                    PartID = p.PartID,
                    PartName = p.PartName,
                    PartNumber = p.PartNumber,
                    Category = p.Category,
                    Brand = p.Brand,
                    Model = p.Model,
                    SellPrice = p.SellPrice.ToString("C"),
                    Stock = p.StockQuantity,
                    Status = p.IsLowStock ? "Low Stock" : "In Stock"
                }).ToList();

                dgvParts.DataSource = parts;

                // Color code low stock rows
                foreach (DataGridViewRow row in dgvParts.Rows)
                {
                    if (row.Cells["Status"].Value.ToString() == "Low Stock")
                    {
                        row.DefaultCellStyle.BackColor = Color.FromArgb(255, 243, 205);
                        row.DefaultCellStyle.ForeColor = Color.FromArgb(133, 100, 4);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading parts: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtSearch.Text))
            {
                LoadParts();
            }
            else
            {
                SearchParts(txtSearch.Text);
            }
        }

        private void SearchParts(string searchTerm)
        {
            try
            {
                var parts = _partsRepository.SearchParts(searchTerm).Select(p => new
                {
                    PartID = p.PartID,
                    PartName = p.PartName,
                    PartNumber = p.PartNumber,
                    Category = p.Category,
                    Brand = p.Brand,
                    Model = p.Model,
                    SellPrice = p.SellPrice.ToString("C"),
                    Stock = p.StockQuantity,
                    Status = p.IsLowStock ? "Low Stock" : "In Stock"
                }).ToList();

                dgvParts.DataSource = parts;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error searching parts: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnSelect_Click(object sender, EventArgs e)
        {
            if (dgvParts.SelectedRows.Count > 0)
            {
                var partId = Convert.ToInt32(dgvParts.SelectedRows[0].Cells["PartID"].Value);
                var stockQuantity = Convert.ToInt32(dgvParts.SelectedRows[0].Cells["Stock"].Value);
                
                if (nudQuantity.Value > stockQuantity)
                {
                    MessageBox.Show($"Insufficient stock. Available quantity: {stockQuantity}", 
                        "Stock Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                SelectedPart = _partsRepository.GetPartById(partId);
                Quantity = (int)nudQuantity.Value;
                IsReplaced = chkIsReplaced.Checked;

                this.DialogResult = DialogResult.OK;
            }
            else
            {
                MessageBox.Show("Please select a part.", "No Selection", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }
    }
}
