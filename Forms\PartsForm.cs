using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using MobileRepairShop.DataAccess;
using MobileRepairShop.Models;

namespace MobileRepairShop.Forms
{
    public partial class PartsForm : Form
    {
        private readonly PartsRepository _partsRepository;
        private DataGridView dgvParts;
        private TextBox txtSearch;
        private Button btnAdd;
        private Button btnEdit;
        private Button btnDelete;
        private Button btnRefresh;

        public PartsForm()
        {
            _partsRepository = new PartsRepository();
            InitializeComponent();
            SetupPartsForm();
            LoadParts();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // PartsForm
            this.AutoScaleDimensions = new SizeF(8F, 16F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.BackColor = Color.FromArgb(240, 240, 240);
            this.ClientSize = new Size(1000, 700);
            this.Name = "PartsForm";
            this.Text = "Parts Management";
            
            this.ResumeLayout(false);
        }

        private void SetupPartsForm()
        {
            // Title
            var titleLabel = new Label
            {
                Text = "Parts Management",
                Font = new Font("Segoe UI", 24, FontStyle.Bold),
                ForeColor = Color.FromArgb(51, 51, 76),
                AutoSize = true,
                Location = new Point(20, 20)
            };

            // Search Panel
            var searchPanel = new Panel
            {
                Location = new Point(20, 70),
                Size = new Size(960, 50),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var searchLabel = new Label
            {
                Text = "Search:",
                Font = new Font("Segoe UI", 10),
                Location = new Point(10, 15),
                AutoSize = true
            };

            txtSearch = new TextBox
            {
                Location = new Point(70, 12),
                Size = new Size(300, 25),
                Font = new Font("Segoe UI", 10)
            };
            txtSearch.TextChanged += TxtSearch_TextChanged;

            btnAdd = CreateButton("Add New Part", new Point(400, 10), Color.FromArgb(40, 167, 69));
            btnEdit = CreateButton("Edit Part", new Point(520, 10), Color.FromArgb(0, 126, 249));
            btnDelete = CreateButton("Delete Part", new Point(620, 10), Color.FromArgb(220, 53, 69));
            btnRefresh = CreateButton("Refresh", new Point(730, 10), Color.FromArgb(108, 117, 125));

            btnAdd.Click += BtnAdd_Click;
            btnEdit.Click += BtnEdit_Click;
            btnDelete.Click += BtnDelete_Click;
            btnRefresh.Click += BtnRefresh_Click;

            searchPanel.Controls.Add(searchLabel);
            searchPanel.Controls.Add(txtSearch);
            searchPanel.Controls.Add(btnAdd);
            searchPanel.Controls.Add(btnEdit);
            searchPanel.Controls.Add(btnDelete);
            searchPanel.Controls.Add(btnRefresh);

            // Parts DataGridView
            dgvParts = new DataGridView
            {
                Location = new Point(20, 140),
                Size = new Size(960, 540),
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };

            StyleDataGridView(dgvParts);

            // Add controls to form
            this.Controls.Add(titleLabel);
            this.Controls.Add(searchPanel);
            this.Controls.Add(dgvParts);
        }

        private Button CreateButton(string text, Point location, Color backColor)
        {
            return new Button
            {
                Text = text,
                Location = location,
                Size = new Size(100, 30),
                BackColor = backColor,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9),
                Cursor = Cursors.Hand,
                FlatAppearance = { BorderSize = 0 }
            };
        }

        private void StyleDataGridView(DataGridView dgv)
        {
            dgv.EnableHeadersVisualStyles = false;
            dgv.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(51, 51, 76);
            dgv.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgv.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10, FontStyle.Bold);
            dgv.DefaultCellStyle.SelectionBackColor = Color.FromArgb(0, 126, 249);
            dgv.DefaultCellStyle.SelectionForeColor = Color.White;
            dgv.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);
            dgv.RowHeadersVisible = false;
        }

        private void LoadParts()
        {
            try
            {
                var parts = _partsRepository.GetAllParts().Select(p => new
                {
                    PartID = p.PartID,
                    PartName = p.PartName,
                    PartNumber = p.PartNumber,
                    Category = p.Category,
                    Brand = p.Brand,
                    Model = p.Model,
                    CostPrice = p.CostPrice.ToString("C"),
                    SellPrice = p.SellPrice.ToString("C"),
                    Stock = p.StockQuantity,
                    MinStock = p.MinStockLevel,
                    Status = p.IsLowStock ? "Low Stock" : "In Stock"
                }).ToList();

                dgvParts.DataSource = parts;

                // Color code low stock rows
                foreach (DataGridViewRow row in dgvParts.Rows)
                {
                    if (row.Cells["Status"].Value.ToString() == "Low Stock")
                    {
                        row.DefaultCellStyle.BackColor = Color.FromArgb(255, 243, 205);
                        row.DefaultCellStyle.ForeColor = Color.FromArgb(133, 100, 4);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading parts: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtSearch.Text))
            {
                LoadParts();
            }
            else
            {
                SearchParts(txtSearch.Text);
            }
        }

        private void SearchParts(string searchTerm)
        {
            try
            {
                var parts = _partsRepository.SearchParts(searchTerm).Select(p => new
                {
                    PartID = p.PartID,
                    PartName = p.PartName,
                    PartNumber = p.PartNumber,
                    Category = p.Category,
                    Brand = p.Brand,
                    Model = p.Model,
                    CostPrice = p.CostPrice.ToString("C"),
                    SellPrice = p.SellPrice.ToString("C"),
                    Stock = p.StockQuantity,
                    MinStock = p.MinStockLevel,
                    Status = p.IsLowStock ? "Low Stock" : "In Stock"
                }).ToList();

                dgvParts.DataSource = parts;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error searching parts: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            var addPartForm = new AddEditPartForm();
            if (addPartForm.ShowDialog() == DialogResult.OK)
            {
                LoadParts();
            }
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            if (dgvParts.SelectedRows.Count > 0)
            {
                var partId = Convert.ToInt32(dgvParts.SelectedRows[0].Cells["PartID"].Value);
                var part = _partsRepository.GetPartById(partId);
                
                var editPartForm = new AddEditPartForm(part);
                if (editPartForm.ShowDialog() == DialogResult.OK)
                {
                    LoadParts();
                }
            }
            else
            {
                MessageBox.Show("Please select a part to edit.", "No Selection", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            if (dgvParts.SelectedRows.Count > 0)
            {
                var partName = dgvParts.SelectedRows[0].Cells["PartName"].Value.ToString();
                var result = MessageBox.Show($"Are you sure you want to delete '{partName}'?", 
                    "Confirm Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                
                if (result == DialogResult.Yes)
                {
                    try
                    {
                        var partId = Convert.ToInt32(dgvParts.SelectedRows[0].Cells["PartID"].Value);
                        _partsRepository.DeletePart(partId);
                        LoadParts();
                        MessageBox.Show("Part deleted successfully.", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Error deleting part: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("Please select a part to delete.", "No Selection", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            txtSearch.Text = "";
            LoadParts();
        }
    }
}
