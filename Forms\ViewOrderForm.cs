using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using MobileRepairShop.Models;
using MobileRepairShop.Services;

namespace MobileRepairShop.Forms
{
    public partial class ViewOrderForm : Form
    {
        private readonly RepairOrder _order;
        private DataGridView dgvParts;
        private Button btnPrintSticker;
        private Button btnPrintReceipt;
        private Button btnSendWhatsApp;
        private Button btnClose;

        public ViewOrderForm(RepairOrder order)
        {
            _order = order;
            InitializeComponent();
            SetupForm();
            LoadOrderDetails();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // ViewOrderForm
            this.AutoScaleDimensions = new SizeF(8F, 16F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.BackColor = Color.FromArgb(240, 240, 240);
            this.ClientSize = new Size(700, 600);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "ViewOrderForm";
            this.StartPosition = FormStartPosition.CenterParent;
            this.Text = $"Order Details - {_order.OrderNumber}";
            
            this.ResumeLayout(false);
        }

        private void SetupForm()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20),
                BackColor = Color.White,
                AutoScroll = true
            };

            int yPos = 20;

            // Title
            var titleLabel = new Label
            {
                Text = $"Order Details - {_order.OrderNumber}",
                Font = new Font("Segoe UI", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(51, 51, 76),
                AutoSize = true,
                Location = new Point(20, yPos)
            };
            yPos += 40;

            // Order Information Section
            var orderInfoPanel = CreateInfoPanel("Order Information", yPos);
            yPos += 30;

            AddInfoRow(orderInfoPanel, "Order Number:", _order.OrderNumber, ref yPos);
            AddInfoRow(orderInfoPanel, "Date:", _order.OrderDate.ToString("MM/dd/yyyy HH:mm"), ref yPos);
            AddInfoRow(orderInfoPanel, "Status:", _order.Status.ToString(), ref yPos);
            AddInfoRow(orderInfoPanel, "Technician:", _order.TechnicianName ?? "Not assigned", ref yPos);
            
            if (_order.CompletionDate.HasValue)
            {
                AddInfoRow(orderInfoPanel, "Completed:", _order.CompletionDate.Value.ToString("MM/dd/yyyy HH:mm"), ref yPos);
            }

            yPos += 20;

            // Customer Information Section
            var customerInfoPanel = CreateInfoPanel("Customer Information", yPos);
            yPos += 30;

            AddInfoRow(customerInfoPanel, "Name:", _order.Customer?.FullName ?? "Unknown", ref yPos);
            AddInfoRow(customerInfoPanel, "Phone:", _order.Customer?.PhoneNumber ?? "Not provided", ref yPos);
            AddInfoRow(customerInfoPanel, "Email:", _order.Customer?.Email ?? "Not provided", ref yPos);

            yPos += 20;

            // Device Information Section
            var deviceInfoPanel = CreateInfoPanel("Device Information", yPos);
            yPos += 30;

            AddInfoRow(deviceInfoPanel, "Model:", _order.PhoneModel, ref yPos);
            AddInfoRow(deviceInfoPanel, "IMEI:", _order.IMEI, ref yPos);
            AddInfoRow(deviceInfoPanel, "Problem:", _order.ProblemDescription, ref yPos);

            yPos += 20;

            // Parts Used Section
            var partsLabel = new Label
            {
                Text = "Parts Used",
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(51, 51, 76),
                AutoSize = true,
                Location = new Point(20, yPos)
            };
            yPos += 30;

            dgvParts = new DataGridView
            {
                Location = new Point(20, yPos),
                Size = new Size(640, 150),
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };
            StyleDataGridView(dgvParts);
            yPos += 170;

            // Cost Summary Section
            var costPanel = CreateInfoPanel("Cost Summary", yPos);
            yPos += 30;

            var partsTotal = _order.OrderParts.Sum(op => op.TotalPrice);
            AddInfoRow(costPanel, "Parts Total:", partsTotal.ToString("C"), ref yPos);
            AddInfoRow(costPanel, "Labor Cost:", _order.LaborCost.ToString("C"), ref yPos);
            
            var totalLabel = new Label
            {
                Text = "Total Amount:",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(51, 51, 76),
                Location = new Point(20, yPos),
                AutoSize = true
            };
            
            var totalValueLabel = new Label
            {
                Text = _order.TotalAmount.ToString("C"),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(40, 167, 69),
                Location = new Point(200, yPos),
                AutoSize = true
            };
            
            costPanel.Controls.Add(totalLabel);
            costPanel.Controls.Add(totalValueLabel);
            yPos += 30;

            // Warranty Information
            if (_order.IsWarranty)
            {
                yPos += 10;
                var warrantyPanel = CreateInfoPanel("Warranty Information", yPos);
                yPos += 30;
                AddInfoRow(warrantyPanel, "Warranty Period:", $"{_order.WarrantyDays} days", ref yPos);
                yPos += 20;
            }

            // Notes
            if (!string.IsNullOrEmpty(_order.Notes))
            {
                var notesLabel = new Label
                {
                    Text = "Notes",
                    Font = new Font("Segoe UI", 14, FontStyle.Bold),
                    ForeColor = Color.FromArgb(51, 51, 76),
                    AutoSize = true,
                    Location = new Point(20, yPos)
                };
                yPos += 30;

                var notesTextBox = new TextBox
                {
                    Text = _order.Notes,
                    Location = new Point(20, yPos),
                    Size = new Size(640, 60),
                    Font = new Font("Segoe UI", 10),
                    Multiline = true,
                    ReadOnly = true,
                    ScrollBars = ScrollBars.Vertical
                };
                yPos += 80;

                panel.Controls.Add(notesLabel);
                panel.Controls.Add(notesTextBox);
            }

            // Action Buttons
            btnPrintSticker = CreateButton("Print Sticker", new Point(20, yPos), Color.FromArgb(255, 193, 7));
            btnPrintReceipt = CreateButton("Print Receipt", new Point(140, yPos), Color.FromArgb(255, 193, 7));
            btnSendWhatsApp = CreateButton("Send WhatsApp", new Point(260, yPos), Color.FromArgb(37, 211, 102));
            btnClose = CreateButton("Close", new Point(580, yPos), Color.FromArgb(108, 117, 125));

            btnPrintSticker.Click += BtnPrintSticker_Click;
            btnPrintReceipt.Click += BtnPrintReceipt_Click;
            btnSendWhatsApp.Click += BtnSendWhatsApp_Click;
            btnClose.Click += (s, e) => this.Close();

            // Add all controls to panel
            panel.Controls.AddRange(new Control[]
            {
                titleLabel, orderInfoPanel, customerInfoPanel, deviceInfoPanel,
                partsLabel, dgvParts, costPanel,
                btnPrintSticker, btnPrintReceipt, btnSendWhatsApp, btnClose
            });

            this.Controls.Add(panel);
        }

        private Panel CreateInfoPanel(string title, int yPos)
        {
            var panel = new Panel
            {
                Location = new Point(20, yPos),
                Size = new Size(640, 100),
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.FromArgb(248, 249, 250)
            };

            var titleLabel = new Label
            {
                Text = title,
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(51, 51, 76),
                Location = new Point(10, 5),
                AutoSize = true
            };

            panel.Controls.Add(titleLabel);
            this.Controls.Add(panel);
            return panel;
        }

        private void AddInfoRow(Panel panel, string label, string value, ref int yPos)
        {
            var lblLabel = new Label
            {
                Text = label,
                Font = new Font("Segoe UI", 10, FontStyle.Regular),
                ForeColor = Color.FromArgb(51, 51, 76),
                Location = new Point(20, yPos - panel.Location.Y + 5),
                AutoSize = true
            };

            var lblValue = new Label
            {
                Text = value,
                Font = new Font("Segoe UI", 10, FontStyle.Regular),
                ForeColor = Color.Black,
                Location = new Point(200, yPos - panel.Location.Y + 5),
                AutoSize = true,
                MaximumSize = new Size(400, 0)
            };

            panel.Controls.Add(lblLabel);
            panel.Controls.Add(lblValue);
            yPos += 20;
        }

        private Button CreateButton(string text, Point location, Color backColor)
        {
            return new Button
            {
                Text = text,
                Location = location,
                Size = new Size(110, 35),
                BackColor = backColor,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10),
                Cursor = Cursors.Hand,
                FlatAppearance = { BorderSize = 0 }
            };
        }

        private void StyleDataGridView(DataGridView dgv)
        {
            dgv.EnableHeadersVisualStyles = false;
            dgv.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(51, 51, 76);
            dgv.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgv.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10, FontStyle.Bold);
            dgv.DefaultCellStyle.SelectionBackColor = Color.FromArgb(0, 126, 249);
            dgv.DefaultCellStyle.SelectionForeColor = Color.White;
            dgv.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);
            dgv.RowHeadersVisible = false;
        }

        private void LoadOrderDetails()
        {
            // Load parts data
            var partsData = _order.OrderParts.Select(op => new
            {
                PartName = op.Part?.PartName ?? "Unknown",
                Quantity = op.Quantity,
                UnitPrice = op.UnitPrice.ToString("C"),
                TotalPrice = op.TotalPrice.ToString("C"),
                Type = op.IsReplaced ? "Replaced" : "Repaired"
            }).ToList();

            dgvParts.DataSource = partsData;
        }

        private void BtnPrintSticker_Click(object sender, EventArgs e)
        {
            var printService = new PrintService();
            printService.PrintSticker(_order);
        }

        private void BtnPrintReceipt_Click(object sender, EventArgs e)
        {
            var printService = new PrintService();
            printService.PrintReceipt(_order);
        }

        private void BtnSendWhatsApp_Click(object sender, EventArgs e)
        {
            var whatsAppService = new WhatsAppService();
            whatsAppService.SendOrderDetails(_order);
        }
    }
}
