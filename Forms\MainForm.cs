using System;
using System.Drawing;
using System.Windows.Forms;
using MobileRepairShop.DataAccess;

namespace MobileRepairShop.Forms
{
    public partial class MainForm : Form
    {
        private Panel sidePanel;
        private Panel contentPanel;
        private Button btnDashboard;
        private Button btnParts;
        private Button btnOrders;
        private Button btnCustomers;
        private Button btnReports;
        private Label lblTitle;
        private Form currentChildForm;

        public MainForm()
        {
            InitializeComponent();
            SetupModernUI();
            ShowDashboard();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // MainForm
            this.AutoScaleDimensions = new SizeF(8F, 16F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1200, 800);
            this.Name = "MainForm";
            this.Text = "Mobile Repair Shop Management System";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;
            this.BackColor = Color.FromArgb(240, 240, 240);
            
            this.ResumeLayout(false);
        }

        private void SetupModernUI()
        {
            // Create side panel
            sidePanel = new Panel
            {
                Dock = DockStyle.Left,
                Width = 250,
                BackColor = Color.FromArgb(51, 51, 76),
                Padding = new Padding(10)
            };

            // Create content panel
            contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(240, 240, 240),
                Padding = new Padding(20)
            };

            // Create title label
            lblTitle = new Label
            {
                Text = "Mobile Repair Shop",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.White,
                AutoSize = true,
                Location = new Point(20, 20)
            };

            // Create navigation buttons
            btnDashboard = CreateNavButton("Dashboard", 80);
            btnOrders = CreateNavButton("Repair Orders", 130);
            btnParts = CreateNavButton("Parts Management", 180);
            btnCustomers = CreateNavButton("Customers", 230);
            btnReports = CreateNavButton("Reports", 280);

            // Add event handlers
            btnDashboard.Click += (s, e) => ShowDashboard();
            btnOrders.Click += (s, e) => ShowOrders();
            btnParts.Click += (s, e) => ShowParts();
            btnCustomers.Click += (s, e) => ShowCustomers();
            btnReports.Click += (s, e) => ShowReports();

            // Add controls to side panel
            sidePanel.Controls.Add(lblTitle);
            sidePanel.Controls.Add(btnDashboard);
            sidePanel.Controls.Add(btnOrders);
            sidePanel.Controls.Add(btnParts);
            sidePanel.Controls.Add(btnCustomers);
            sidePanel.Controls.Add(btnReports);

            // Add panels to main form
            this.Controls.Add(contentPanel);
            this.Controls.Add(sidePanel);
        }

        private Button CreateNavButton(string text, int y)
        {
            return new Button
            {
                Text = text,
                Size = new Size(200, 40),
                Location = new Point(20, y),
                FlatStyle = FlatStyle.Flat,
                BackColor = Color.FromArgb(39, 39, 58),
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 10, FontStyle.Regular),
                TextAlign = ContentAlignment.MiddleLeft,
                Padding = new Padding(10, 0, 0, 0),
                Cursor = Cursors.Hand,
                FlatAppearance = { BorderSize = 0 }
            };
        }

        private void OpenChildForm(Form childForm)
        {
            if (currentChildForm != null)
            {
                currentChildForm.Close();
            }
            currentChildForm = childForm;
            childForm.TopLevel = false;
            childForm.FormBorderStyle = FormBorderStyle.None;
            childForm.Dock = DockStyle.Fill;
            contentPanel.Controls.Add(childForm);
            contentPanel.Tag = childForm;
            childForm.BringToFront();
            childForm.Show();
        }

        private void ShowDashboard()
        {
            SetActiveButton(btnDashboard);
            OpenChildForm(new DashboardForm());
        }

        private void ShowOrders()
        {
            SetActiveButton(btnOrders);
            OpenChildForm(new OrdersForm());
        }

        private void ShowParts()
        {
            SetActiveButton(btnParts);
            OpenChildForm(new PartsForm());
        }

        private void ShowCustomers()
        {
            SetActiveButton(btnCustomers);
            OpenChildForm(new CustomersForm());
        }

        private void ShowReports()
        {
            SetActiveButton(btnReports);
            OpenChildForm(new ReportsForm());
        }

        private void SetActiveButton(Button activeButton)
        {
            // Reset all buttons
            foreach (Control control in sidePanel.Controls)
            {
                if (control is Button btn)
                {
                    btn.BackColor = Color.FromArgb(39, 39, 58);
                }
            }
            // Set active button
            activeButton.BackColor = Color.FromArgb(0, 126, 249);
        }
    }
}
