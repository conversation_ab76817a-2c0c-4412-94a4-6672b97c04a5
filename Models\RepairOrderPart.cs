using System;

namespace MobileRepairShop.Models
{
    public class RepairOrderPart
    {
        public int OrderPartID { get; set; }
        public int OrderID { get; set; }
        public int PartID { get; set; }
        public int Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TotalPrice => Quantity * UnitPrice;
        public bool IsReplaced { get; set; }
        public DateTime CreatedDate { get; set; }

        // Navigation properties
        public RepairOrder RepairOrder { get; set; }
        public Part Part { get; set; }

        public RepairOrderPart()
        {
            Quantity = 1;
            IsReplaced = true;
            CreatedDate = DateTime.Now;
        }

        public override string ToString()
        {
            return $"{Part?.PartName} x{Quantity} - ${TotalPrice:F2}";
        }
    }
}
