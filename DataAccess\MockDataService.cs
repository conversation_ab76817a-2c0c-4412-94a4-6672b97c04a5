using System;
using System.Collections.Generic;
using System.Linq;
using MobileRepairShop.Models;

namespace MobileRepairShop.DataAccess
{
    public static class MockDataService
    {
        private static List<Customer> _customers;
        private static List<Part> _parts;
        private static List<RepairOrder> _orders;
        private static int _nextCustomerId = 1;
        private static int _nextPartId = 1;
        private static int _nextOrderId = 1;

        static MockDataService()
        {
            InitializeMockData();
        }

        private static void InitializeMockData()
        {
            // Initialize Customers
            _customers = new List<Customer>
            {
                new Customer { CustomerID = _nextCustomerId++, FirstName = "John", LastName = "Doe", PhoneNumber = "+1234567890", Email = "<EMAIL>", Address = "123 Main St, City" },
                new Customer { CustomerID = _nextCustomerId++, FirstName = "Jane", LastName = "Smith", PhoneNumber = "+1234567891", Email = "<EMAIL>", Address = "456 Oak Ave, City" },
                new Customer { CustomerID = _nextCustomerId++, FirstName = "<PERSON>", LastName = "Johnson", PhoneNumber = "+1234567892", Email = "<EMAIL>", Address = "789 Pine Rd, City" }
            };

            // Initialize Parts
            _parts = new List<Part>
            {
                new Part { PartID = _nextPartId++, PartName = "iPhone 12 Screen", PartNumber = "IP12-SCR-001", Description = "Original iPhone 12 LCD Screen Assembly", Category = "Screen", Brand = "Apple", Model = "iPhone 12", CostPrice = 45.00m, SellPrice = 89.99m, StockQuantity = 15, MinStockLevel = 5 },
                new Part { PartID = _nextPartId++, PartName = "Samsung S21 Battery", PartNumber = "SAM-S21-BAT", Description = "Samsung Galaxy S21 Original Battery", Category = "Battery", Brand = "Samsung", Model = "Galaxy S21", CostPrice = 12.50m, SellPrice = 24.99m, StockQuantity = 25, MinStockLevel = 10 },
                new Part { PartID = _nextPartId++, PartName = "iPhone 11 Charging Port", PartNumber = "IP11-CHG-001", Description = "iPhone 11 Lightning Charging Port Flex", Category = "Charging Port", Brand = "Apple", Model = "iPhone 11", CostPrice = 8.00m, SellPrice = 19.99m, StockQuantity = 20, MinStockLevel = 8 },
                new Part { PartID = _nextPartId++, PartName = "Universal Screen Protector", PartNumber = "UNIV-PROT-001", Description = "Tempered Glass Screen Protector", Category = "Accessory", Brand = "Generic", Model = "Universal", CostPrice = 2.00m, SellPrice = 9.99m, StockQuantity = 100, MinStockLevel = 20 },
                new Part { PartID = _nextPartId++, PartName = "Phone Opening Tools Kit", PartNumber = "TOOL-KIT-001", Description = "Professional Phone Repair Tools Set", Category = "Tools", Brand = "Generic", Model = "Universal", CostPrice = 15.00m, SellPrice = 29.99m, StockQuantity = 3, MinStockLevel = 5 }
            };

            // Initialize Orders
            _orders = new List<RepairOrder>
            {
                new RepairOrder 
                { 
                    OrderID = _nextOrderId++, 
                    OrderNumber = "ORD20241201001", 
                    CustomerID = 1, 
                    PhoneModel = "iPhone 12", 
                    IMEI = "123456789012345", 
                    ProblemDescription = "Cracked screen, needs replacement", 
                    OrderDate = DateTime.Now.AddDays(-2), 
                    Status = OrderStatus.InProgress, 
                    TotalAmount = 109.98m, 
                    LaborCost = 20.00m, 
                    TechnicianName = "Tech Support",
                    IsWarranty = true,
                    WarrantyDays = 30,
                    OrderParts = new List<RepairOrderPart>
                    {
                        new RepairOrderPart { PartID = 1, Quantity = 1, UnitPrice = 89.99m, IsReplaced = true }
                    }
                },
                new RepairOrder 
                { 
                    OrderID = _nextOrderId++, 
                    OrderNumber = "ORD20241201002", 
                    CustomerID = 2, 
                    PhoneModel = "Samsung Galaxy S21", 
                    IMEI = "987654321098765", 
                    ProblemDescription = "Battery drains quickly, replacement needed", 
                    OrderDate = DateTime.Now.AddDays(-1), 
                    Status = OrderStatus.Completed, 
                    CompletionDate = DateTime.Now,
                    TotalAmount = 44.99m, 
                    LaborCost = 20.00m, 
                    TechnicianName = "Tech Support",
                    IsWarranty = true,
                    WarrantyDays = 30,
                    OrderParts = new List<RepairOrderPart>
                    {
                        new RepairOrderPart { PartID = 2, Quantity = 1, UnitPrice = 24.99m, IsReplaced = true }
                    }
                }
            };

            // Link customers to orders
            foreach (var order in _orders)
            {
                order.Customer = _customers.FirstOrDefault(c => c.CustomerID == order.CustomerID);
                foreach (var orderPart in order.OrderParts)
                {
                    orderPart.Part = _parts.FirstOrDefault(p => p.PartID == orderPart.PartID);
                }
            }
        }

        // Customer methods
        public static List<Customer> GetAllCustomers() => _customers.ToList();
        public static Customer GetCustomerById(int id) => _customers.FirstOrDefault(c => c.CustomerID == id);
        public static List<Customer> SearchCustomers(string searchTerm) => 
            _customers.Where(c => c.FirstName.Contains(searchTerm) || c.LastName.Contains(searchTerm) || 
                                 c.PhoneNumber.Contains(searchTerm) || c.Email.Contains(searchTerm)).ToList();
        
        public static int AddCustomer(Customer customer)
        {
            customer.CustomerID = _nextCustomerId++;
            customer.CreatedDate = DateTime.Now;
            customer.UpdatedDate = DateTime.Now;
            _customers.Add(customer);
            return customer.CustomerID;
        }

        public static void UpdateCustomer(Customer customer)
        {
            var existing = _customers.FirstOrDefault(c => c.CustomerID == customer.CustomerID);
            if (existing != null)
            {
                existing.FirstName = customer.FirstName;
                existing.LastName = customer.LastName;
                existing.PhoneNumber = customer.PhoneNumber;
                existing.Email = customer.Email;
                existing.Address = customer.Address;
                existing.UpdatedDate = DateTime.Now;
            }
        }

        public static void DeleteCustomer(int id)
        {
            _customers.RemoveAll(c => c.CustomerID == id);
        }

        // Parts methods
        public static List<Part> GetAllParts() => _parts.Where(p => p.IsActive).ToList();
        public static Part GetPartById(int id) => _parts.FirstOrDefault(p => p.PartID == id);
        public static List<Part> GetLowStockParts() => _parts.Where(p => p.StockQuantity <= p.MinStockLevel && p.IsActive).ToList();
        public static List<Part> SearchParts(string searchTerm) => 
            _parts.Where(p => p.PartName.Contains(searchTerm) || p.PartNumber.Contains(searchTerm) || 
                             p.Brand.Contains(searchTerm) || p.Model.Contains(searchTerm)).ToList();

        public static int AddPart(Part part)
        {
            part.PartID = _nextPartId++;
            part.CreatedDate = DateTime.Now;
            part.UpdatedDate = DateTime.Now;
            _parts.Add(part);
            return part.PartID;
        }

        public static void UpdatePart(Part part)
        {
            var existing = _parts.FirstOrDefault(p => p.PartID == part.PartID);
            if (existing != null)
            {
                existing.PartName = part.PartName;
                existing.PartNumber = part.PartNumber;
                existing.Description = part.Description;
                existing.Category = part.Category;
                existing.Brand = part.Brand;
                existing.Model = part.Model;
                existing.CostPrice = part.CostPrice;
                existing.SellPrice = part.SellPrice;
                existing.StockQuantity = part.StockQuantity;
                existing.MinStockLevel = part.MinStockLevel;
                existing.IsActive = part.IsActive;
                existing.UpdatedDate = DateTime.Now;
            }
        }

        public static void UpdatePartStock(int partId, int quantityUsed)
        {
            var part = _parts.FirstOrDefault(p => p.PartID == partId);
            if (part != null)
            {
                part.StockQuantity -= quantityUsed;
                part.UpdatedDate = DateTime.Now;
            }
        }

        public static void DeletePart(int id)
        {
            var part = _parts.FirstOrDefault(p => p.PartID == id);
            if (part != null)
            {
                part.IsActive = false;
                part.UpdatedDate = DateTime.Now;
            }
        }

        // Order methods
        public static List<RepairOrder> GetAllOrders() => _orders.ToList();
        public static RepairOrder GetOrderById(int id) => _orders.FirstOrDefault(o => o.OrderID == id);
        public static List<RepairOrder> GetOrdersByStatus(OrderStatus status) => _orders.Where(o => o.Status == status).ToList();

        public static int AddOrder(RepairOrder order)
        {
            order.OrderID = _nextOrderId++;
            order.CreatedDate = DateTime.Now;
            order.UpdatedDate = DateTime.Now;
            order.Customer = GetCustomerById(order.CustomerID);
            
            foreach (var orderPart in order.OrderParts)
            {
                orderPart.Part = GetPartById(orderPart.PartID);
                UpdatePartStock(orderPart.PartID, orderPart.Quantity);
            }
            
            _orders.Add(order);
            return order.OrderID;
        }

        public static void UpdateOrder(RepairOrder order)
        {
            var existing = _orders.FirstOrDefault(o => o.OrderID == order.OrderID);
            if (existing != null)
            {
                existing.CustomerID = order.CustomerID;
                existing.PhoneModel = order.PhoneModel;
                existing.IMEI = order.IMEI;
                existing.ProblemDescription = order.ProblemDescription;
                existing.CompletionDate = order.CompletionDate;
                existing.Status = order.Status;
                existing.TotalAmount = order.TotalAmount;
                existing.LaborCost = order.LaborCost;
                existing.Notes = order.Notes;
                existing.TechnicianName = order.TechnicianName;
                existing.IsWarranty = order.IsWarranty;
                existing.WarrantyDays = order.WarrantyDays;
                existing.UpdatedDate = DateTime.Now;
                existing.Customer = GetCustomerById(order.CustomerID);
            }
        }

        public static void UpdateOrderStatus(int orderId, OrderStatus status)
        {
            var order = _orders.FirstOrDefault(o => o.OrderID == orderId);
            if (order != null)
            {
                order.Status = status;
                if (status == OrderStatus.Completed)
                {
                    order.CompletionDate = DateTime.Now;
                }
                order.UpdatedDate = DateTime.Now;
            }
        }
    }
}
