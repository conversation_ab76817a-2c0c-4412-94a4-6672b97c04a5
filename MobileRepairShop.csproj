<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{12345678-1234-1234-1234-123456789012}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>MobileRepairShop</RootNamespace>
    <AssemblyName>MobileRepairShop</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <ApplicationIcon>icon.ico</ApplicationIcon>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Web" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="Microsoft.CSharp" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="DataAccess\CustomerRepository.cs" />
    <Compile Include="DataAccess\DatabaseConnection.cs" />
    <Compile Include="DataAccess\PartsRepository.cs" />
    <Compile Include="DataAccess\RepairOrderRepository.cs" />
    <Compile Include="Forms\AddEditCustomerForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\AddEditOrderForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\AddEditPartForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\CustomersForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\DashboardForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\MainForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\OrdersForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\PartsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ReportsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SelectPartForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ViewOrderForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Models\Customer.cs" />
    <Compile Include="Models\Part.cs" />
    <Compile Include="Models\RepairOrder.cs" />
    <Compile Include="Models\RepairOrderPart.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Services\PrintService.cs" />
    <Compile Include="Services\WhatsAppService.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
    <None Include="Database\CreateDatabase.sql" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Forms\AddEditCustomerForm.resx">
      <DependentUpon>AddEditCustomerForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\AddEditOrderForm.resx">
      <DependentUpon>AddEditOrderForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\AddEditPartForm.resx">
      <DependentUpon>AddEditPartForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\CustomersForm.resx">
      <DependentUpon>CustomersForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\DashboardForm.resx">
      <DependentUpon>DashboardForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\MainForm.resx">
      <DependentUpon>MainForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\OrdersForm.resx">
      <DependentUpon>OrdersForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\PartsForm.resx">
      <DependentUpon>PartsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ReportsForm.resx">
      <DependentUpon>ReportsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SelectPartForm.resx">
      <DependentUpon>SelectPartForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ViewOrderForm.resx">
      <DependentUpon>ViewOrderForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="bin\" />
    <Folder Include="obj\" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>
