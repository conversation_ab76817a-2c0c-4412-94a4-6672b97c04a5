using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using MobileRepairShop.DataAccess;
using MobileRepairShop.Models;

namespace MobileRepairShop.Forms
{
    public partial class OrdersForm : Form
    {
        private readonly RepairOrderRepository _orderRepository;
        private DataGridView dgvOrders;
        private ComboBox cmbStatusFilter;
        private TextBox txtSearch;
        private Button btnNewOrder;
        private Button btnEditOrder;
        private Button btnViewOrder;
        private Button btnPrintSticker;
        private Button btnPrintReceipt;
        private Button btnSendWhatsApp;
        private Button btnRefresh;

        public OrdersForm()
        {
            _orderRepository = new RepairOrderRepository();
            InitializeComponent();
            SetupOrdersForm();
            LoadOrders();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // OrdersForm
            this.AutoScaleDimensions = new SizeF(8F, 16F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.BackColor = Color.FromArgb(240, 240, 240);
            this.ClientSize = new Size(1000, 700);
            this.Name = "OrdersForm";
            this.Text = "Repair Orders";
            
            this.ResumeLayout(false);
        }

        private void SetupOrdersForm()
        {
            // Title
            var titleLabel = new Label
            {
                Text = "Repair Orders",
                Font = new Font("Segoe UI", 24, FontStyle.Bold),
                ForeColor = Color.FromArgb(51, 51, 76),
                AutoSize = true,
                Location = new Point(20, 20)
            };

            // Filter and Search Panel
            var filterPanel = new Panel
            {
                Location = new Point(20, 70),
                Size = new Size(960, 50),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var statusLabel = new Label
            {
                Text = "Status:",
                Font = new Font("Segoe UI", 10),
                Location = new Point(10, 15),
                AutoSize = true
            };

            cmbStatusFilter = new ComboBox
            {
                Location = new Point(60, 12),
                Size = new Size(120, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Segoe UI", 10)
            };
            cmbStatusFilter.Items.AddRange(new[] { "All", "Pending", "InProgress", "Completed", "Delivered", "Cancelled" });
            cmbStatusFilter.SelectedIndex = 0;
            cmbStatusFilter.SelectedIndexChanged += CmbStatusFilter_SelectedIndexChanged;

            var searchLabel = new Label
            {
                Text = "Search:",
                Font = new Font("Segoe UI", 10),
                Location = new Point(200, 15),
                AutoSize = true
            };

            txtSearch = new TextBox
            {
                Location = new Point(260, 12),
                Size = new Size(200, 25),
                Font = new Font("Segoe UI", 10)
            };
            txtSearch.TextChanged += TxtSearch_TextChanged;

            btnNewOrder = CreateButton("New Order", new Point(480, 10), Color.FromArgb(40, 167, 69));
            btnEditOrder = CreateButton("Edit", new Point(590, 10), Color.FromArgb(0, 126, 249));
            btnViewOrder = CreateButton("View", new Point(680, 10), Color.FromArgb(108, 117, 125));
            btnRefresh = CreateButton("Refresh", new Point(770, 10), Color.FromArgb(108, 117, 125));

            btnNewOrder.Click += BtnNewOrder_Click;
            btnEditOrder.Click += BtnEditOrder_Click;
            btnViewOrder.Click += BtnViewOrder_Click;
            btnRefresh.Click += BtnRefresh_Click;

            filterPanel.Controls.AddRange(new Control[]
            {
                statusLabel, cmbStatusFilter, searchLabel, txtSearch,
                btnNewOrder, btnEditOrder, btnViewOrder, btnRefresh
            });

            // Action Panel
            var actionPanel = new Panel
            {
                Location = new Point(20, 140),
                Size = new Size(960, 50),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            btnPrintSticker = CreateButton("Print Sticker", new Point(10, 10), Color.FromArgb(255, 193, 7));
            btnPrintReceipt = CreateButton("Print Receipt", new Point(120, 10), Color.FromArgb(255, 193, 7));
            btnSendWhatsApp = CreateButton("Send WhatsApp", new Point(240, 10), Color.FromArgb(37, 211, 102));

            btnPrintSticker.Click += BtnPrintSticker_Click;
            btnPrintReceipt.Click += BtnPrintReceipt_Click;
            btnSendWhatsApp.Click += BtnSendWhatsApp_Click;

            actionPanel.Controls.AddRange(new Control[]
            {
                btnPrintSticker, btnPrintReceipt, btnSendWhatsApp
            });

            // Orders DataGridView
            dgvOrders = new DataGridView
            {
                Location = new Point(20, 210),
                Size = new Size(960, 470),
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };

            StyleDataGridView(dgvOrders);

            // Add controls to form
            this.Controls.Add(titleLabel);
            this.Controls.Add(filterPanel);
            this.Controls.Add(actionPanel);
            this.Controls.Add(dgvOrders);
        }

        private Button CreateButton(string text, Point location, Color backColor)
        {
            return new Button
            {
                Text = text,
                Location = location,
                Size = new Size(100, 30),
                BackColor = backColor,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9),
                Cursor = Cursors.Hand,
                FlatAppearance = { BorderSize = 0 }
            };
        }

        private void StyleDataGridView(DataGridView dgv)
        {
            dgv.EnableHeadersVisualStyles = false;
            dgv.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(51, 51, 76);
            dgv.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgv.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10, FontStyle.Bold);
            dgv.DefaultCellStyle.SelectionBackColor = Color.FromArgb(0, 126, 249);
            dgv.DefaultCellStyle.SelectionForeColor = Color.White;
            dgv.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);
            dgv.RowHeadersVisible = false;
        }

        private void LoadOrders()
        {
            try
            {
                var orders = _orderRepository.GetAllOrders().Select(o => new
                {
                    OrderID = o.OrderID,
                    OrderNumber = o.OrderNumber,
                    Customer = o.Customer?.FullName ?? "Unknown",
                    PhoneModel = o.PhoneModel,
                    IMEI = o.IMEI,
                    Problem = o.ProblemDescription.Length > 50 ? o.ProblemDescription.Substring(0, 50) + "..." : o.ProblemDescription,
                    Status = o.Status.ToString(),
                    Amount = o.TotalAmount.ToString("C"),
                    OrderDate = o.OrderDate.ToString("MM/dd/yyyy"),
                    Technician = o.TechnicianName
                }).ToList();

                dgvOrders.DataSource = orders;

                // Color code rows by status
                foreach (DataGridViewRow row in dgvOrders.Rows)
                {
                    var status = row.Cells["Status"].Value.ToString();
                    switch (status)
                    {
                        case "Pending":
                            row.DefaultCellStyle.BackColor = Color.FromArgb(255, 243, 205);
                            break;
                        case "InProgress":
                            row.DefaultCellStyle.BackColor = Color.FromArgb(217, 237, 247);
                            break;
                        case "Completed":
                            row.DefaultCellStyle.BackColor = Color.FromArgb(212, 237, 218);
                            break;
                        case "Delivered":
                            row.DefaultCellStyle.BackColor = Color.FromArgb(209, 236, 241);
                            break;
                        case "Cancelled":
                            row.DefaultCellStyle.BackColor = Color.FromArgb(248, 215, 218);
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading orders: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CmbStatusFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            FilterOrders();
        }

        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            FilterOrders();
        }

        private void FilterOrders()
        {
            try
            {
                var allOrders = _orderRepository.GetAllOrders();
                
                // Filter by status
                if (cmbStatusFilter.SelectedItem.ToString() != "All")
                {
                    var status = (OrderStatus)Enum.Parse(typeof(OrderStatus), cmbStatusFilter.SelectedItem.ToString());
                    allOrders = allOrders.Where(o => o.Status == status).ToList();
                }

                // Filter by search term
                if (!string.IsNullOrWhiteSpace(txtSearch.Text))
                {
                    var searchTerm = txtSearch.Text.ToLower();
                    allOrders = allOrders.Where(o => 
                        o.OrderNumber.ToLower().Contains(searchTerm) ||
                        o.Customer?.FullName.ToLower().Contains(searchTerm) == true ||
                        o.PhoneModel.ToLower().Contains(searchTerm) ||
                        o.IMEI.ToLower().Contains(searchTerm)
                    ).ToList();
                }

                var filteredOrders = allOrders.Select(o => new
                {
                    OrderID = o.OrderID,
                    OrderNumber = o.OrderNumber,
                    Customer = o.Customer?.FullName ?? "Unknown",
                    PhoneModel = o.PhoneModel,
                    IMEI = o.IMEI,
                    Problem = o.ProblemDescription.Length > 50 ? o.ProblemDescription.Substring(0, 50) + "..." : o.ProblemDescription,
                    Status = o.Status.ToString(),
                    Amount = o.TotalAmount.ToString("C"),
                    OrderDate = o.OrderDate.ToString("MM/dd/yyyy"),
                    Technician = o.TechnicianName
                }).ToList();

                dgvOrders.DataSource = filteredOrders;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error filtering orders: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnNewOrder_Click(object sender, EventArgs e)
        {
            var newOrderForm = new AddEditOrderForm();
            if (newOrderForm.ShowDialog() == DialogResult.OK)
            {
                LoadOrders();
            }
        }

        private void BtnEditOrder_Click(object sender, EventArgs e)
        {
            if (dgvOrders.SelectedRows.Count > 0)
            {
                var orderId = Convert.ToInt32(dgvOrders.SelectedRows[0].Cells["OrderID"].Value);
                var order = _orderRepository.GetOrderById(orderId);
                
                var editOrderForm = new AddEditOrderForm(order);
                if (editOrderForm.ShowDialog() == DialogResult.OK)
                {
                    LoadOrders();
                }
            }
            else
            {
                MessageBox.Show("Please select an order to edit.", "No Selection", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void BtnViewOrder_Click(object sender, EventArgs e)
        {
            if (dgvOrders.SelectedRows.Count > 0)
            {
                var orderId = Convert.ToInt32(dgvOrders.SelectedRows[0].Cells["OrderID"].Value);
                var order = _orderRepository.GetOrderById(orderId);
                
                var viewOrderForm = new ViewOrderForm(order);
                viewOrderForm.ShowDialog();
            }
            else
            {
                MessageBox.Show("Please select an order to view.", "No Selection", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void BtnPrintSticker_Click(object sender, EventArgs e)
        {
            if (dgvOrders.SelectedRows.Count > 0)
            {
                var orderId = Convert.ToInt32(dgvOrders.SelectedRows[0].Cells["OrderID"].Value);
                var order = _orderRepository.GetOrderById(orderId);
                
                var printService = new PrintService();
                printService.PrintSticker(order);
            }
            else
            {
                MessageBox.Show("Please select an order to print sticker.", "No Selection", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void BtnPrintReceipt_Click(object sender, EventArgs e)
        {
            if (dgvOrders.SelectedRows.Count > 0)
            {
                var orderId = Convert.ToInt32(dgvOrders.SelectedRows[0].Cells["OrderID"].Value);
                var order = _orderRepository.GetOrderById(orderId);
                
                var printService = new PrintService();
                printService.PrintReceipt(order);
            }
            else
            {
                MessageBox.Show("Please select an order to print receipt.", "No Selection", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void BtnSendWhatsApp_Click(object sender, EventArgs e)
        {
            if (dgvOrders.SelectedRows.Count > 0)
            {
                var orderId = Convert.ToInt32(dgvOrders.SelectedRows[0].Cells["OrderID"].Value);
                var order = _orderRepository.GetOrderById(orderId);
                
                var whatsAppService = new WhatsAppService();
                whatsAppService.SendOrderDetails(order);
            }
            else
            {
                MessageBox.Show("Please select an order to send WhatsApp message.", "No Selection", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            cmbStatusFilter.SelectedIndex = 0;
            txtSearch.Text = "";
            LoadOrders();
        }
    }
}
