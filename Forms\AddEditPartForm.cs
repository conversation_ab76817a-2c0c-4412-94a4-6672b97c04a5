using System;
using System.Drawing;
using System.Windows.Forms;
using MobileRepairShop.DataAccess;
using MobileRepairShop.Models;

namespace MobileRepairShop.Forms
{
    public partial class AddEditPartForm : Form
    {
        private readonly PartsRepository _partsRepository;
        private readonly Part _part;
        private readonly bool _isEditMode;

        private TextBox txtPartName;
        private TextBox txtPartNumber;
        private TextBox txtDescription;
        private ComboBox cmbCategory;
        private TextBox txtBrand;
        private TextBox txtModel;
        private NumericUpDown nudCostPrice;
        private NumericUpDown nudSellPrice;
        private NumericUpDown nudStockQuantity;
        private NumericUpDown nudMinStockLevel;
        private CheckBox chkIsActive;
        private Button btnSave;
        private Button btnCancel;

        public AddEditPartForm(Part part = null)
        {
            _partsRepository = new PartsRepository();
            _part = part ?? new Part();
            _isEditMode = part != null;
            
            InitializeComponent();
            SetupForm();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // AddEditPartForm
            this.AutoScaleDimensions = new SizeF(8F, 16F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.BackColor = Color.FromArgb(240, 240, 240);
            this.ClientSize = new Size(500, 600);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "AddEditPartForm";
            this.StartPosition = FormStartPosition.CenterParent;
            this.Text = _isEditMode ? "Edit Part" : "Add New Part";
            
            this.ResumeLayout(false);
        }

        private void SetupForm()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20),
                BackColor = Color.White
            };

            // Title
            var titleLabel = new Label
            {
                Text = _isEditMode ? "Edit Part" : "Add New Part",
                Font = new Font("Segoe UI", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(51, 51, 76),
                AutoSize = true,
                Location = new Point(20, 20)
            };

            // Part Name
            var lblPartName = CreateLabel("Part Name:", 70);
            txtPartName = CreateTextBox(100);

            // Part Number
            var lblPartNumber = CreateLabel("Part Number:", 130);
            txtPartNumber = CreateTextBox(160);

            // Description
            var lblDescription = CreateLabel("Description:", 190);
            txtDescription = CreateTextBox(220, multiline: true, height: 60);

            // Category
            var lblCategory = CreateLabel("Category:", 290);
            cmbCategory = new ComboBox
            {
                Location = new Point(20, 320),
                Size = new Size(200, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Segoe UI", 10)
            };
            cmbCategory.Items.AddRange(new[] { "Screen", "Battery", "Charging Port", "Camera", "Speaker", "Microphone", "Tools", "Accessory", "Other" });

            // Brand
            var lblBrand = CreateLabel("Brand:", 350);
            txtBrand = CreateTextBox(380);

            // Model
            var lblModel = CreateLabel("Model:", 410);
            txtModel = CreateTextBox(440);

            // Cost Price
            var lblCostPrice = CreateLabel("Cost Price:", 70, 250);
            nudCostPrice = CreateNumericUpDown(100, 250);

            // Sell Price
            var lblSellPrice = CreateLabel("Sell Price:", 130, 250);
            nudSellPrice = CreateNumericUpDown(160, 250);

            // Stock Quantity
            var lblStockQuantity = CreateLabel("Stock Quantity:", 190, 250);
            nudStockQuantity = CreateNumericUpDown(220, 250);
            nudStockQuantity.DecimalPlaces = 0;
            nudStockQuantity.Maximum = 10000;

            // Min Stock Level
            var lblMinStockLevel = CreateLabel("Min Stock Level:", 250, 250);
            nudMinStockLevel = CreateNumericUpDown(280, 250);
            nudMinStockLevel.DecimalPlaces = 0;
            nudMinStockLevel.Maximum = 1000;

            // Is Active
            chkIsActive = new CheckBox
            {
                Text = "Active",
                Location = new Point(20, 480),
                AutoSize = true,
                Font = new Font("Segoe UI", 10),
                Checked = true
            };

            // Buttons
            btnSave = new Button
            {
                Text = "Save",
                Location = new Point(280, 520),
                Size = new Size(80, 35),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10),
                Cursor = Cursors.Hand,
                FlatAppearance = { BorderSize = 0 }
            };
            btnSave.Click += BtnSave_Click;

            btnCancel = new Button
            {
                Text = "Cancel",
                Location = new Point(380, 520),
                Size = new Size(80, 35),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10),
                Cursor = Cursors.Hand,
                FlatAppearance = { BorderSize = 0 }
            };
            btnCancel.Click += (s, e) => this.DialogResult = DialogResult.Cancel;

            // Add controls to panel
            panel.Controls.AddRange(new Control[]
            {
                titleLabel, lblPartName, txtPartName, lblPartNumber, txtPartNumber,
                lblDescription, txtDescription, lblCategory, cmbCategory,
                lblBrand, txtBrand, lblModel, txtModel,
                lblCostPrice, nudCostPrice, lblSellPrice, nudSellPrice,
                lblStockQuantity, nudStockQuantity, lblMinStockLevel, nudMinStockLevel,
                chkIsActive, btnSave, btnCancel
            });

            this.Controls.Add(panel);
        }

        private Label CreateLabel(string text, int y, int x = 20)
        {
            return new Label
            {
                Text = text,
                Location = new Point(x, y),
                AutoSize = true,
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(51, 51, 76)
            };
        }

        private TextBox CreateTextBox(int y, int x = 20, bool multiline = false, int height = 25)
        {
            return new TextBox
            {
                Location = new Point(x, y),
                Size = new Size(200, height),
                Font = new Font("Segoe UI", 10),
                Multiline = multiline
            };
        }

        private NumericUpDown CreateNumericUpDown(int y, int x = 20)
        {
            return new NumericUpDown
            {
                Location = new Point(x, y),
                Size = new Size(200, 25),
                Font = new Font("Segoe UI", 10),
                DecimalPlaces = 2,
                Maximum = 99999.99m,
                Minimum = 0
            };
        }

        private void LoadData()
        {
            if (_isEditMode)
            {
                txtPartName.Text = _part.PartName;
                txtPartNumber.Text = _part.PartNumber;
                txtDescription.Text = _part.Description;
                cmbCategory.Text = _part.Category;
                txtBrand.Text = _part.Brand;
                txtModel.Text = _part.Model;
                nudCostPrice.Value = _part.CostPrice;
                nudSellPrice.Value = _part.SellPrice;
                nudStockQuantity.Value = _part.StockQuantity;
                nudMinStockLevel.Value = _part.MinStockLevel;
                chkIsActive.Checked = _part.IsActive;
            }
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (ValidateInput())
            {
                try
                {
                    _part.PartName = txtPartName.Text.Trim();
                    _part.PartNumber = txtPartNumber.Text.Trim();
                    _part.Description = txtDescription.Text.Trim();
                    _part.Category = cmbCategory.Text;
                    _part.Brand = txtBrand.Text.Trim();
                    _part.Model = txtModel.Text.Trim();
                    _part.CostPrice = nudCostPrice.Value;
                    _part.SellPrice = nudSellPrice.Value;
                    _part.StockQuantity = (int)nudStockQuantity.Value;
                    _part.MinStockLevel = (int)nudMinStockLevel.Value;
                    _part.IsActive = chkIsActive.Checked;

                    if (_isEditMode)
                    {
                        _partsRepository.UpdatePart(_part);
                        MessageBox.Show("Part updated successfully.", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        _partsRepository.AddPart(_part);
                        MessageBox.Show("Part added successfully.", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }

                    this.DialogResult = DialogResult.OK;
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error saving part: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtPartName.Text))
            {
                MessageBox.Show("Part name is required.", "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPartName.Focus();
                return false;
            }

            if (nudSellPrice.Value <= nudCostPrice.Value)
            {
                MessageBox.Show("Sell price must be greater than cost price.", "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                nudSellPrice.Focus();
                return false;
            }

            return true;
        }
    }
}
