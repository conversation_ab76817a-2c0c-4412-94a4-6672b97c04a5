using System;
using System.Collections.Generic;

namespace MobileRepairShop.Models
{
    public enum OrderStatus
    {
        Pending,
        InProgress,
        Completed,
        Delivered,
        Cancelled
    }

    public class RepairOrder
    {
        public int OrderID { get; set; }
        public string OrderNumber { get; set; }
        public int CustomerID { get; set; }
        public string PhoneModel { get; set; }
        public string IMEI { get; set; }
        public string ProblemDescription { get; set; }
        public DateTime OrderDate { get; set; }
        public DateTime? CompletionDate { get; set; }
        public OrderStatus Status { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal LaborCost { get; set; }
        public string Notes { get; set; }
        public string TechnicianName { get; set; }
        public bool IsWarranty { get; set; }
        public int WarrantyDays { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime UpdatedDate { get; set; }

        // Navigation properties
        public Customer Customer { get; set; }
        public List<RepairOrderPart> OrderParts { get; set; }

        public decimal PartsTotal
        {
            get
            {
                decimal total = 0;
                if (OrderParts != null)
                {
                    foreach (var part in OrderParts)
                    {
                        total += part.TotalPrice;
                    }
                }
                return total;
            }
        }

        public RepairOrder()
        {
            OrderDate = DateTime.Now;
            CreatedDate = DateTime.Now;
            UpdatedDate = DateTime.Now;
            Status = OrderStatus.Pending;
            WarrantyDays = 30;
            OrderParts = new List<RepairOrderPart>();
            OrderNumber = GenerateOrderNumber();
        }

        private string GenerateOrderNumber()
        {
            return $"ORD{DateTime.Now:yyyyMMdd}{DateTime.Now.Ticks.ToString().Substring(10)}";
        }

        public override string ToString()
        {
            return $"{OrderNumber} - {PhoneModel} ({Status})";
        }
    }
}
