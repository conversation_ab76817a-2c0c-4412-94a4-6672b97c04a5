using System;
using System.Windows.Forms;
using MobileRepairShop.Forms;
using MobileRepairShop.DataAccess;

namespace MobileRepairShop
{
    static class Program
    {
        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            // Test database connection on startup
            if (!DatabaseConnection.TestConnection())
            {
                MessageBox.Show("Unable to connect to the database. Please check your connection settings.", 
                    "Database Connection Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            Application.Run(new MainForm());
        }
    }
}
