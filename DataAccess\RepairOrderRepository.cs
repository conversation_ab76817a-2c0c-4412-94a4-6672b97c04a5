using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using MobileRepairShop.Models;

namespace MobileRepairShop.DataAccess
{
    public class RepairOrderRepository
    {
        private readonly CustomerRepository _customerRepository;
        private readonly PartsRepository _partsRepository;

        public RepairOrderRepository()
        {
            _customerRepository = new CustomerRepository();
            _partsRepository = new PartsRepository();
        }

        public List<RepairOrder> GetAllOrders()
        {
            var orders = new List<RepairOrder>();
            string sql = @"SELECT OrderID, OrderNumber, CustomerID, PhoneModel, IMEI, ProblemDescription, 
                          OrderDate, CompletionDate, Status, TotalAmount, LaborCost, Notes, TechnicianName, 
                          IsWarranty, WarrantyDays, CreatedDate, UpdatedDate 
                          FROM RepairOrders ORDER BY OrderDate DESC";

            using (var connection = DatabaseConnection.GetConnection())
            {
                connection.Open();
                using (var reader = DatabaseConnection.ExecuteReader(sql, connection))
                {
                    while (reader.Read())
                    {
                        var order = MapReaderToRepairOrder(reader);
                        order.Customer = _customerRepository.GetCustomerById(order.CustomerID);
                        order.OrderParts = GetOrderParts(order.OrderID);
                        orders.Add(order);
                    }
                }
            }
            return orders;
        }

        public RepairOrder GetOrderById(int orderId)
        {
            string sql = @"SELECT OrderID, OrderNumber, CustomerID, PhoneModel, IMEI, ProblemDescription, 
                          OrderDate, CompletionDate, Status, TotalAmount, LaborCost, Notes, TechnicianName, 
                          IsWarranty, WarrantyDays, CreatedDate, UpdatedDate 
                          FROM RepairOrders WHERE OrderID = @OrderID";
            
            using (var connection = DatabaseConnection.GetConnection())
            {
                connection.Open();
                using (var reader = DatabaseConnection.ExecuteReader(sql, connection, new SqlParameter("@OrderID", orderId)))
                {
                    if (reader.Read())
                    {
                        var order = MapReaderToRepairOrder(reader);
                        order.Customer = _customerRepository.GetCustomerById(order.CustomerID);
                        order.OrderParts = GetOrderParts(order.OrderID);
                        return order;
                    }
                }
            }
            return null;
        }

        public List<RepairOrder> GetOrdersByStatus(OrderStatus status)
        {
            var orders = new List<RepairOrder>();
            string sql = @"SELECT OrderID, OrderNumber, CustomerID, PhoneModel, IMEI, ProblemDescription, 
                          OrderDate, CompletionDate, Status, TotalAmount, LaborCost, Notes, TechnicianName, 
                          IsWarranty, WarrantyDays, CreatedDate, UpdatedDate 
                          FROM RepairOrders WHERE Status = @Status ORDER BY OrderDate DESC";

            using (var connection = DatabaseConnection.GetConnection())
            {
                connection.Open();
                using (var reader = DatabaseConnection.ExecuteReader(sql, connection, new SqlParameter("@Status", status.ToString())))
                {
                    while (reader.Read())
                    {
                        var order = MapReaderToRepairOrder(reader);
                        order.Customer = _customerRepository.GetCustomerById(order.CustomerID);
                        order.OrderParts = GetOrderParts(order.OrderID);
                        orders.Add(order);
                    }
                }
            }
            return orders;
        }

        public int AddOrder(RepairOrder order)
        {
            using (var connection = DatabaseConnection.GetConnection())
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        // Insert the main order
                        string orderSql = @"INSERT INTO RepairOrders (OrderNumber, CustomerID, PhoneModel, IMEI, ProblemDescription, 
                                           OrderDate, Status, TotalAmount, LaborCost, Notes, TechnicianName, IsWarranty, WarrantyDays, CreatedDate, UpdatedDate) 
                                           VALUES (@OrderNumber, @CustomerID, @PhoneModel, @IMEI, @ProblemDescription, 
                                           @OrderDate, @Status, @TotalAmount, @LaborCost, @Notes, @TechnicianName, @IsWarranty, @WarrantyDays, @CreatedDate, @UpdatedDate);
                                           SELECT SCOPE_IDENTITY();";

                        var orderParameters = new[]
                        {
                            new SqlParameter("@OrderNumber", order.OrderNumber),
                            new SqlParameter("@CustomerID", order.CustomerID),
                            new SqlParameter("@PhoneModel", order.PhoneModel ?? ""),
                            new SqlParameter("@IMEI", order.IMEI ?? ""),
                            new SqlParameter("@ProblemDescription", order.ProblemDescription ?? ""),
                            new SqlParameter("@OrderDate", order.OrderDate),
                            new SqlParameter("@Status", order.Status.ToString()),
                            new SqlParameter("@TotalAmount", order.TotalAmount),
                            new SqlParameter("@LaborCost", order.LaborCost),
                            new SqlParameter("@Notes", order.Notes ?? ""),
                            new SqlParameter("@TechnicianName", order.TechnicianName ?? ""),
                            new SqlParameter("@IsWarranty", order.IsWarranty),
                            new SqlParameter("@WarrantyDays", order.WarrantyDays),
                            new SqlParameter("@CreatedDate", order.CreatedDate),
                            new SqlParameter("@UpdatedDate", order.UpdatedDate)
                        };

                        using (var command = new SqlCommand(orderSql, connection, transaction))
                        {
                            command.Parameters.AddRange(orderParameters);
                            var orderId = Convert.ToInt32(command.ExecuteScalar());

                            // Insert order parts
                            foreach (var orderPart in order.OrderParts)
                            {
                                string partSql = @"INSERT INTO RepairOrderParts (OrderID, PartID, Quantity, UnitPrice, IsReplaced, CreatedDate) 
                                                  VALUES (@OrderID, @PartID, @Quantity, @UnitPrice, @IsReplaced, @CreatedDate)";

                                var partParameters = new[]
                                {
                                    new SqlParameter("@OrderID", orderId),
                                    new SqlParameter("@PartID", orderPart.PartID),
                                    new SqlParameter("@Quantity", orderPart.Quantity),
                                    new SqlParameter("@UnitPrice", orderPart.UnitPrice),
                                    new SqlParameter("@IsReplaced", orderPart.IsReplaced),
                                    new SqlParameter("@CreatedDate", orderPart.CreatedDate)
                                };

                                using (var partCommand = new SqlCommand(partSql, connection, transaction))
                                {
                                    partCommand.Parameters.AddRange(partParameters);
                                    partCommand.ExecuteNonQuery();
                                }

                                // Update part stock
                                _partsRepository.UpdatePartStock(orderPart.PartID, orderPart.Quantity);
                            }

                            transaction.Commit();
                            return orderId;
                        }
                    }
                    catch
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
            }
        }

        public void UpdateOrder(RepairOrder order)
        {
            string sql = @"UPDATE RepairOrders SET 
                          CustomerID = @CustomerID, PhoneModel = @PhoneModel, IMEI = @IMEI, 
                          ProblemDescription = @ProblemDescription, CompletionDate = @CompletionDate, 
                          Status = @Status, TotalAmount = @TotalAmount, LaborCost = @LaborCost, 
                          Notes = @Notes, TechnicianName = @TechnicianName, IsWarranty = @IsWarranty, 
                          WarrantyDays = @WarrantyDays, UpdatedDate = @UpdatedDate 
                          WHERE OrderID = @OrderID";

            var parameters = new[]
            {
                new SqlParameter("@OrderID", order.OrderID),
                new SqlParameter("@CustomerID", order.CustomerID),
                new SqlParameter("@PhoneModel", order.PhoneModel ?? ""),
                new SqlParameter("@IMEI", order.IMEI ?? ""),
                new SqlParameter("@ProblemDescription", order.ProblemDescription ?? ""),
                new SqlParameter("@CompletionDate", (object)order.CompletionDate ?? DBNull.Value),
                new SqlParameter("@Status", order.Status.ToString()),
                new SqlParameter("@TotalAmount", order.TotalAmount),
                new SqlParameter("@LaborCost", order.LaborCost),
                new SqlParameter("@Notes", order.Notes ?? ""),
                new SqlParameter("@TechnicianName", order.TechnicianName ?? ""),
                new SqlParameter("@IsWarranty", order.IsWarranty),
                new SqlParameter("@WarrantyDays", order.WarrantyDays),
                new SqlParameter("@UpdatedDate", DateTime.Now)
            };

            DatabaseConnection.ExecuteNonQuery(sql, parameters);
        }

        public void UpdateOrderStatus(int orderId, OrderStatus status)
        {
            string sql = "UPDATE RepairOrders SET Status = @Status, UpdatedDate = @UpdatedDate WHERE OrderID = @OrderID";
            if (status == OrderStatus.Completed)
            {
                sql = "UPDATE RepairOrders SET Status = @Status, CompletionDate = @CompletionDate, UpdatedDate = @UpdatedDate WHERE OrderID = @OrderID";
            }

            var parameters = new List<SqlParameter>
            {
                new SqlParameter("@OrderID", orderId),
                new SqlParameter("@Status", status.ToString()),
                new SqlParameter("@UpdatedDate", DateTime.Now)
            };

            if (status == OrderStatus.Completed)
            {
                parameters.Add(new SqlParameter("@CompletionDate", DateTime.Now));
            }

            DatabaseConnection.ExecuteNonQuery(sql, parameters.ToArray());
        }

        private List<RepairOrderPart> GetOrderParts(int orderId)
        {
            var orderParts = new List<RepairOrderPart>();
            string sql = @"SELECT rop.OrderPartID, rop.OrderID, rop.PartID, rop.Quantity, rop.UnitPrice, rop.IsReplaced, rop.CreatedDate
                          FROM RepairOrderParts rop WHERE rop.OrderID = @OrderID";

            using (var connection = DatabaseConnection.GetConnection())
            {
                connection.Open();
                using (var reader = DatabaseConnection.ExecuteReader(sql, connection, new SqlParameter("@OrderID", orderId)))
                {
                    while (reader.Read())
                    {
                        var orderPart = new RepairOrderPart
                        {
                            OrderPartID = reader.GetInt32("OrderPartID"),
                            OrderID = reader.GetInt32("OrderID"),
                            PartID = reader.GetInt32("PartID"),
                            Quantity = reader.GetInt32("Quantity"),
                            UnitPrice = reader.GetDecimal("UnitPrice"),
                            IsReplaced = reader.GetBoolean("IsReplaced"),
                            CreatedDate = reader.GetDateTime("CreatedDate")
                        };
                        orderPart.Part = _partsRepository.GetPartById(orderPart.PartID);
                        orderParts.Add(orderPart);
                    }
                }
            }
            return orderParts;
        }

        private RepairOrder MapReaderToRepairOrder(SqlDataReader reader)
        {
            return new RepairOrder
            {
                OrderID = reader.GetInt32("OrderID"),
                OrderNumber = reader.GetString("OrderNumber"),
                CustomerID = reader.GetInt32("CustomerID"),
                PhoneModel = reader.IsDBNull(reader.GetOrdinal("PhoneModel")) ? "" : reader.GetString("PhoneModel"),
                IMEI = reader.IsDBNull(reader.GetOrdinal("IMEI")) ? "" : reader.GetString("IMEI"),
                ProblemDescription = reader.IsDBNull(reader.GetOrdinal("ProblemDescription")) ? "" : reader.GetString("ProblemDescription"),
                OrderDate = reader.GetDateTime("OrderDate"),
                CompletionDate = reader.IsDBNull(reader.GetOrdinal("CompletionDate")) ? (DateTime?)null : reader.GetDateTime("CompletionDate"),
                Status = (OrderStatus)Enum.Parse(typeof(OrderStatus), reader.GetString("Status")),
                TotalAmount = reader.GetDecimal("TotalAmount"),
                LaborCost = reader.GetDecimal("LaborCost"),
                Notes = reader.IsDBNull(reader.GetOrdinal("Notes")) ? "" : reader.GetString("Notes"),
                TechnicianName = reader.IsDBNull(reader.GetOrdinal("TechnicianName")) ? "" : reader.GetString("TechnicianName"),
                IsWarranty = reader.GetBoolean("IsWarranty"),
                WarrantyDays = reader.GetInt32("WarrantyDays"),
                CreatedDate = reader.GetDateTime("CreatedDate"),
                UpdatedDate = reader.GetDateTime("UpdatedDate")
            };
        }
    }
}
