using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using MobileRepairShop.Models;

namespace MobileRepairShop.DataAccess
{
    public class RepairOrderRepository
    {
        public List<RepairOrder> GetAllOrders()
        {
            return MockDataService.GetAllOrders();
        }

        public RepairOrder GetOrderById(int orderId)
        {
            return MockDataService.GetOrderById(orderId);
        }

        public List<RepairOrder> GetOrdersByStatus(OrderStatus status)
        {
            return MockDataService.GetOrdersByStatus(status);
        }

        public int AddOrder(RepairOrder order)
        {
            return MockDataService.AddOrder(order);
        }

        public void UpdateOrder(RepairOrder order)
        {
            MockDataService.UpdateOrder(order);
        }

        public void UpdateOrderStatus(int orderId, OrderStatus status)
        {
            MockDataService.UpdateOrderStatus(orderId, status);
        }
    }
}
