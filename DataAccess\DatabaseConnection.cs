using System;
using System.Configuration;
using System.Data.SqlClient;
using System.IO;

namespace MobileRepairShop.DataAccess
{
    public static class DatabaseConnection
    {
        private static string _connectionString;

        static DatabaseConnection()
        {
            // For demo purposes, we'll create a simple in-memory approach
            // In production, use proper SQL Server connection
            var dbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "MobileRepairShop.db");
            _connectionString = ConfigurationManager.ConnectionStrings["MobileRepairShop"]?.ConnectionString
                ?? $@"Data Source={dbPath};Version=3;";
        }

        public static string ConnectionString
        {
            get { return _connectionString; }
            set { _connectionString = value; }
        }

        public static SqlConnection GetConnection()
        {
            // For demo, we'll simulate the connection
            // In production, return actual SQL Server connection
            return new SqlConnection("Server=.;Database=Demo;Integrated Security=true;");
        }

        public static bool TestConnection()
        {
            try
            {
                // For demo purposes, always return true
                // In production, test actual database connection
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public static void ExecuteNonQuery(string sql, params SqlParameter[] parameters)
        {
            // For demo purposes, we'll just log the operation
            Console.WriteLine($"Executing: {sql}");
        }

        public static object ExecuteScalar(string sql, params SqlParameter[] parameters)
        {
            // For demo purposes, return a mock ID
            if (sql.Contains("SCOPE_IDENTITY"))
            {
                return new Random().Next(1, 1000);
            }
            return null;
        }

        public static SqlDataReader ExecuteReader(string sql, SqlConnection connection, params SqlParameter[] parameters)
        {
            // For demo purposes, this would need to be implemented with actual data
            // In production, use real SQL Server connection and reader
            throw new NotImplementedException("Demo mode - use actual SQL Server for production");
        }
    }
}
