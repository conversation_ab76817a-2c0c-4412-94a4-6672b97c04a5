using System;
using System.Configuration;
using System.Data.SqlClient;

namespace MobileRepairShop.DataAccess
{
    public static class DatabaseConnection
    {
        private static string _connectionString;

        static DatabaseConnection()
        {
            // Default connection string - can be overridden in app.config
            _connectionString = ConfigurationManager.ConnectionStrings["MobileRepairShop"]?.ConnectionString
                ?? @"Data Source=.\SQLEXPRESS;Initial Catalog=MobileRepairShop;Integrated Security=True;Connect Timeout=30;Encrypt=False;TrustServerCertificate=True;ApplicationIntent=ReadWrite;MultiSubnetFailover=False";
        }

        public static string ConnectionString
        {
            get { return _connectionString; }
            set { _connectionString = value; }
        }

        public static SqlConnection GetConnection()
        {
            return new SqlConnection(_connectionString);
        }

        public static bool TestConnection()
        {
            try
            {
                using (var connection = GetConnection())
                {
                    connection.Open();
                    return true;
                }
            }
            catch (Exception)
            {
                return false;
            }
        }

        public static void ExecuteNonQuery(string sql, params SqlParameter[] parameters)
        {
            using (var connection = GetConnection())
            {
                connection.Open();
                using (var command = new SqlCommand(sql, connection))
                {
                    if (parameters != null)
                    {
                        command.Parameters.AddRange(parameters);
                    }
                    command.ExecuteNonQuery();
                }
            }
        }

        public static object ExecuteScalar(string sql, params SqlParameter[] parameters)
        {
            using (var connection = GetConnection())
            {
                connection.Open();
                using (var command = new SqlCommand(sql, connection))
                {
                    if (parameters != null)
                    {
                        command.Parameters.AddRange(parameters);
                    }
                    return command.ExecuteScalar();
                }
            }
        }

        public static SqlDataReader ExecuteReader(string sql, SqlConnection connection, params SqlParameter[] parameters)
        {
            using (var command = new SqlCommand(sql, connection))
            {
                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters);
                }
                return command.ExecuteReader();
            }
        }
    }
}
