@echo off
echo Building Mobile Repair Shop Application...

REM Try to find MSBuild
set MSBUILD_PATH=""

REM Check for Visual Studio 2022
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
    goto :build
)

REM Check for Visual Studio 2019
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"
    goto :build
)

REM Check for Build Tools
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\MSBuild.exe"
    goto :build
)

REM Try using dotnet build as fallback
echo MSBuild not found, trying dotnet build...
dotnet build MobileRepairShop.csproj --configuration Release
if %ERRORLEVEL% EQU 0 (
    echo Build successful!
    echo Executable created in bin\Release\
    goto :end
) else (
    echo Build failed with dotnet
    goto :error
)

:build
echo Using MSBuild: %MSBUILD_PATH%
%MSBUILD_PATH% MobileRepairShop.csproj /p:Configuration=Release /p:Platform="Any CPU"

if %ERRORLEVEL% EQU 0 (
    echo Build successful!
    echo Executable created in bin\Release\
) else (
    echo Build failed
    goto :error
)
goto :end

:error
echo.
echo Build failed. Please ensure you have:
echo 1. Visual Studio 2019/2022 or Build Tools installed
echo 2. .NET Framework 4.6.2 or later
echo 3. All source files are present
pause
exit /b 1

:end
echo.
echo To run the application:
echo cd bin\Release
echo MobileRepairShop.exe
pause
