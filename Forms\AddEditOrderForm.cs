using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using MobileRepairShop.DataAccess;
using MobileRepairShop.Models;

namespace MobileRepairShop.Forms
{
    public partial class AddEditOrderForm : Form
    {
        private readonly RepairOrderRepository _orderRepository;
        private readonly CustomerRepository _customerRepository;
        private readonly PartsRepository _partsRepository;
        private readonly RepairOrder _order;
        private readonly bool _isEditMode;

        private ComboBox cmbCustomer;
        private Button btnNewCustomer;
        private TextBox txtPhoneModel;
        private TextBox txtIMEI;
        private TextBox txtProblemDescription;
        private ComboBox cmbStatus;
        private NumericUpDown nudLaborCost;
        private TextBox txtTechnician;
        private CheckBox chkWarranty;
        private NumericUpDown nudWarrantyDays;
        private TextBox txtNotes;
        private DataGridView dgvParts;
        private Button btnAddPart;
        private Button btnRemovePart;
        private Label lblTotal;
        private Button btnSave;
        private Button btnCancel;

        public AddEditOrderForm(RepairOrder order = null)
        {
            _orderRepository = new RepairOrderRepository();
            _customerRepository = new CustomerRepository();
            _partsRepository = new PartsRepository();
            _order = order ?? new RepairOrder();
            _isEditMode = order != null;
            
            InitializeComponent();
            SetupForm();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // AddEditOrderForm
            this.AutoScaleDimensions = new SizeF(8F, 16F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.BackColor = Color.FromArgb(240, 240, 240);
            this.ClientSize = new Size(800, 700);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "AddEditOrderForm";
            this.StartPosition = FormStartPosition.CenterParent;
            this.Text = _isEditMode ? "Edit Repair Order" : "New Repair Order";
            
            this.ResumeLayout(false);
        }

        private void SetupForm()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20),
                BackColor = Color.White,
                AutoScroll = true
            };

            // Title
            var titleLabel = new Label
            {
                Text = _isEditMode ? "Edit Repair Order" : "New Repair Order",
                Font = new Font("Segoe UI", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(51, 51, 76),
                AutoSize = true,
                Location = new Point(20, 20)
            };

            // Customer Section
            var lblCustomer = CreateLabel("Customer:", 70);
            cmbCustomer = new ComboBox
            {
                Location = new Point(20, 100),
                Size = new Size(300, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Segoe UI", 10)
            };

            btnNewCustomer = new Button
            {
                Text = "New Customer",
                Location = new Point(330, 100),
                Size = new Size(100, 25),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9),
                FlatAppearance = { BorderSize = 0 }
            };
            btnNewCustomer.Click += BtnNewCustomer_Click;

            // Phone Details
            var lblPhoneModel = CreateLabel("Phone Model:", 140);
            txtPhoneModel = CreateTextBox(170);

            var lblIMEI = CreateLabel("IMEI:", 210);
            txtIMEI = CreateTextBox(240);

            // Problem Description
            var lblProblem = CreateLabel("Problem Description:", 280);
            txtProblemDescription = new TextBox
            {
                Location = new Point(20, 310),
                Size = new Size(400, 60),
                Font = new Font("Segoe UI", 10),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical
            };

            // Status and Labor
            var lblStatus = CreateLabel("Status:", 140, 450);
            cmbStatus = new ComboBox
            {
                Location = new Point(450, 170),
                Size = new Size(150, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Segoe UI", 10)
            };
            cmbStatus.Items.AddRange(Enum.GetNames(typeof(OrderStatus)));

            var lblLaborCost = CreateLabel("Labor Cost:", 210, 450);
            nudLaborCost = new NumericUpDown
            {
                Location = new Point(450, 240),
                Size = new Size(150, 25),
                Font = new Font("Segoe UI", 10),
                DecimalPlaces = 2,
                Maximum = 99999.99m,
                Minimum = 0
            };

            // Technician
            var lblTechnician = CreateLabel("Technician:", 280, 450);
            txtTechnician = new TextBox
            {
                Location = new Point(450, 310),
                Size = new Size(200, 25),
                Font = new Font("Segoe UI", 10)
            };

            // Warranty
            chkWarranty = new CheckBox
            {
                Text = "Warranty",
                Location = new Point(450, 350),
                AutoSize = true,
                Font = new Font("Segoe UI", 10)
            };

            nudWarrantyDays = new NumericUpDown
            {
                Location = new Point(550, 348),
                Size = new Size(60, 25),
                Font = new Font("Segoe UI", 10),
                Maximum = 365,
                Minimum = 0,
                Value = 30
            };

            var lblDays = new Label
            {
                Text = "days",
                Location = new Point(620, 350),
                AutoSize = true,
                Font = new Font("Segoe UI", 10)
            };

            // Notes
            var lblNotes = CreateLabel("Notes:", 380);
            txtNotes = new TextBox
            {
                Location = new Point(20, 410),
                Size = new Size(400, 60),
                Font = new Font("Segoe UI", 10),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical
            };

            // Parts Section
            var lblParts = CreateLabel("Parts Used:", 490);
            
            dgvParts = new DataGridView
            {
                Location = new Point(20, 520),
                Size = new Size(600, 150),
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };

            btnAddPart = CreateButton("Add Part", new Point(630, 520), Color.FromArgb(40, 167, 69));
            btnRemovePart = CreateButton("Remove", new Point(630, 560), Color.FromArgb(220, 53, 69));

            btnAddPart.Click += BtnAddPart_Click;
            btnRemovePart.Click += BtnRemovePart_Click;

            // Total
            lblTotal = new Label
            {
                Text = "Total: $0.00",
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(51, 51, 76),
                Location = new Point(450, 400),
                AutoSize = true
            };

            // Buttons
            btnSave = new Button
            {
                Text = "Save Order",
                Location = new Point(520, 620),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10),
                Cursor = Cursors.Hand,
                FlatAppearance = { BorderSize = 0 }
            };
            btnSave.Click += BtnSave_Click;

            btnCancel = new Button
            {
                Text = "Cancel",
                Location = new Point(640, 620),
                Size = new Size(80, 35),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10),
                Cursor = Cursors.Hand,
                FlatAppearance = { BorderSize = 0 }
            };
            btnCancel.Click += (s, e) => this.DialogResult = DialogResult.Cancel;

            // Add controls to panel
            panel.Controls.AddRange(new Control[]
            {
                titleLabel, lblCustomer, cmbCustomer, btnNewCustomer,
                lblPhoneModel, txtPhoneModel, lblIMEI, txtIMEI,
                lblProblem, txtProblemDescription, lblStatus, cmbStatus,
                lblLaborCost, nudLaborCost, lblTechnician, txtTechnician,
                chkWarranty, nudWarrantyDays, lblDays, lblNotes, txtNotes,
                lblParts, dgvParts, btnAddPart, btnRemovePart, lblTotal,
                btnSave, btnCancel
            });

            this.Controls.Add(panel);
        }

        private Label CreateLabel(string text, int y, int x = 20)
        {
            return new Label
            {
                Text = text,
                Location = new Point(x, y),
                AutoSize = true,
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(51, 51, 76)
            };
        }

        private TextBox CreateTextBox(int y, int x = 20)
        {
            return new TextBox
            {
                Location = new Point(x, y),
                Size = new Size(200, 25),
                Font = new Font("Segoe UI", 10)
            };
        }

        private Button CreateButton(string text, Point location, Color backColor)
        {
            return new Button
            {
                Text = text,
                Location = location,
                Size = new Size(80, 30),
                BackColor = backColor,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9),
                Cursor = Cursors.Hand,
                FlatAppearance = { BorderSize = 0 }
            };
        }

        private void LoadData()
        {
            // Load customers
            var customers = _customerRepository.GetAllCustomers();
            cmbCustomer.DataSource = customers;
            cmbCustomer.DisplayMember = "FullName";
            cmbCustomer.ValueMember = "CustomerID";

            if (_isEditMode)
            {
                cmbCustomer.SelectedValue = _order.CustomerID;
                txtPhoneModel.Text = _order.PhoneModel;
                txtIMEI.Text = _order.IMEI;
                txtProblemDescription.Text = _order.ProblemDescription;
                cmbStatus.SelectedItem = _order.Status.ToString();
                nudLaborCost.Value = _order.LaborCost;
                txtTechnician.Text = _order.TechnicianName;
                chkWarranty.Checked = _order.IsWarranty;
                nudWarrantyDays.Value = _order.WarrantyDays;
                txtNotes.Text = _order.Notes;
                
                LoadOrderParts();
            }
            else
            {
                cmbStatus.SelectedItem = "Pending";
                txtTechnician.Text = System.Configuration.ConfigurationManager.AppSettings["DefaultTechnician"] ?? "";
            }

            UpdateTotal();
        }

        private void LoadOrderParts()
        {
            var partsData = _order.OrderParts.Select(op => new
            {
                PartID = op.PartID,
                PartName = op.Part?.PartName ?? "Unknown",
                Quantity = op.Quantity,
                UnitPrice = op.UnitPrice.ToString("C"),
                TotalPrice = op.TotalPrice.ToString("C"),
                IsReplaced = op.IsReplaced ? "Replaced" : "Repaired"
            }).ToList();

            dgvParts.DataSource = partsData;
        }

        private void UpdateTotal()
        {
            var partsTotal = _order.OrderParts.Sum(op => op.TotalPrice);
            var total = partsTotal + nudLaborCost.Value;
            lblTotal.Text = $"Total: {total:C}";
        }

        private void BtnNewCustomer_Click(object sender, EventArgs e)
        {
            var newCustomerForm = new AddEditCustomerForm();
            if (newCustomerForm.ShowDialog() == DialogResult.OK)
            {
                // Reload customers
                var customers = _customerRepository.GetAllCustomers();
                cmbCustomer.DataSource = customers;
                cmbCustomer.DisplayMember = "FullName";
                cmbCustomer.ValueMember = "CustomerID";
            }
        }

        private void BtnAddPart_Click(object sender, EventArgs e)
        {
            var selectPartForm = new SelectPartForm();
            if (selectPartForm.ShowDialog() == DialogResult.OK)
            {
                var selectedPart = selectPartForm.SelectedPart;
                var quantity = selectPartForm.Quantity;
                var isReplaced = selectPartForm.IsReplaced;

                var orderPart = new RepairOrderPart
                {
                    PartID = selectedPart.PartID,
                    Part = selectedPart,
                    Quantity = quantity,
                    UnitPrice = selectedPart.SellPrice,
                    IsReplaced = isReplaced
                };

                _order.OrderParts.Add(orderPart);
                LoadOrderParts();
                UpdateTotal();
            }
        }

        private void BtnRemovePart_Click(object sender, EventArgs e)
        {
            if (dgvParts.SelectedRows.Count > 0)
            {
                var partId = Convert.ToInt32(dgvParts.SelectedRows[0].Cells["PartID"].Value);
                var orderPart = _order.OrderParts.FirstOrDefault(op => op.PartID == partId);
                if (orderPart != null)
                {
                    _order.OrderParts.Remove(orderPart);
                    LoadOrderParts();
                    UpdateTotal();
                }
            }
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (ValidateInput())
            {
                try
                {
                    _order.CustomerID = (int)cmbCustomer.SelectedValue;
                    _order.PhoneModel = txtPhoneModel.Text.Trim();
                    _order.IMEI = txtIMEI.Text.Trim();
                    _order.ProblemDescription = txtProblemDescription.Text.Trim();
                    _order.Status = (OrderStatus)Enum.Parse(typeof(OrderStatus), cmbStatus.SelectedItem.ToString());
                    _order.LaborCost = nudLaborCost.Value;
                    _order.TechnicianName = txtTechnician.Text.Trim();
                    _order.IsWarranty = chkWarranty.Checked;
                    _order.WarrantyDays = (int)nudWarrantyDays.Value;
                    _order.Notes = txtNotes.Text.Trim();
                    _order.TotalAmount = _order.OrderParts.Sum(op => op.TotalPrice) + _order.LaborCost;

                    if (_isEditMode)
                    {
                        _orderRepository.UpdateOrder(_order);
                        MessageBox.Show("Order updated successfully.", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        _orderRepository.AddOrder(_order);
                        MessageBox.Show("Order created successfully.", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }

                    this.DialogResult = DialogResult.OK;
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error saving order: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private bool ValidateInput()
        {
            if (cmbCustomer.SelectedValue == null)
            {
                MessageBox.Show("Please select a customer.", "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtPhoneModel.Text))
            {
                MessageBox.Show("Phone model is required.", "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPhoneModel.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtIMEI.Text))
            {
                MessageBox.Show("IMEI is required.", "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtIMEI.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtProblemDescription.Text))
            {
                MessageBox.Show("Problem description is required.", "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtProblemDescription.Focus();
                return false;
            }

            return true;
        }
    }
}
