using System;

namespace MobileRepairShop.Models
{
    public class Part
    {
        public int PartID { get; set; }
        public string PartName { get; set; }
        public string PartNumber { get; set; }
        public string Description { get; set; }
        public string Category { get; set; }
        public string Brand { get; set; }
        public string Model { get; set; }
        public decimal CostPrice { get; set; }
        public decimal SellPrice { get; set; }
        public int StockQuantity { get; set; }
        public int MinStockLevel { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime UpdatedDate { get; set; }

        public decimal ProfitMargin => SellPrice - CostPrice;
        public bool IsLowStock => StockQuantity <= MinStockLevel;

        public Part()
        {
            IsActive = true;
            CreatedDate = DateTime.Now;
            UpdatedDate = DateTime.Now;
            MinStockLevel = 5;
        }

        public override string ToString()
        {
            return $"{PartName} - {Brand} {Model} (Stock: {StockQuantity})";
        }
    }
}
