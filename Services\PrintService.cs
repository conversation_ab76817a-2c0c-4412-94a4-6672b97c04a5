using System;
using System.Configuration;
using System.Drawing;
using System.Drawing.Printing;
using System.IO;
using System.Runtime.InteropServices;
using System.Text;
using System.Windows.Forms;
using MobileRepairShop.Models;

namespace MobileRepairShop.Services
{
    public class PrintService
    {
        private readonly string _thermalPrinterName;
        private readonly string _receiptPrinterName;
        private readonly string _companyName;
        private readonly string _companyAddress;
        private readonly string _companyPhone;

        public PrintService()
        {
            _thermalPrinterName = ConfigurationManager.AppSettings["ThermalPrinterName"] ?? "Generic / Text Only";
            _receiptPrinterName = ConfigurationManager.AppSettings["ReceiptPrinterName"] ?? "Generic / Text Only";
            _companyName = ConfigurationManager.AppSettings["CompanyName"] ?? "Mobile Repair Shop";
            _companyAddress = ConfigurationManager.AppSettings["CompanyAddress"] ?? "";
            _companyPhone = ConfigurationManager.AppSettings["CompanyPhone"] ?? "";
        }

        public void PrintSticker(RepairOrder order)
        {
            try
            {
                var printDocument = new PrintDocument();
                printDocument.PrinterSettings.PrinterName = _thermalPrinterName;
                printDocument.DefaultPageSettings.PaperSize = new PaperSize("Sticker", 200, 150); // 2" x 1.5"
                printDocument.PrintPage += (sender, e) => PrintStickerContent(e, order);

                var printDialog = new PrintDialog();
                printDialog.Document = printDocument;
                
                if (printDialog.ShowDialog() == DialogResult.OK)
                {
                    printDocument.Print();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error printing sticker: {ex.Message}", "Print Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        public void PrintReceipt(RepairOrder order)
        {
            try
            {
                var printDocument = new PrintDocument();
                printDocument.PrinterSettings.PrinterName = _receiptPrinterName;
                printDocument.DefaultPageSettings.PaperSize = new PaperSize("Receipt", 315, 600); // 80mm width
                printDocument.PrintPage += (sender, e) => PrintReceiptContent(e, order);

                var printDialog = new PrintDialog();
                printDialog.Document = printDocument;
                
                if (printDialog.ShowDialog() == DialogResult.OK)
                {
                    printDocument.Print();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error printing receipt: {ex.Message}", "Print Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void PrintStickerContent(PrintPageEventArgs e, RepairOrder order)
        {
            var graphics = e.Graphics;
            var font = new Font("Arial", 8, FontStyle.Regular);
            var boldFont = new Font("Arial", 9, FontStyle.Bold);
            var brush = Brushes.Black;
            float yPos = 10;
            float leftMargin = 10;

            // Company name
            graphics.DrawString(_companyName, boldFont, brush, leftMargin, yPos);
            yPos += 15;

            // Order number
            graphics.DrawString($"Order: {order.OrderNumber}", boldFont, brush, leftMargin, yPos);
            yPos += 15;

            // Customer name
            graphics.DrawString($"Customer: {order.Customer?.FullName ?? "Unknown"}", font, brush, leftMargin, yPos);
            yPos += 12;

            // Phone model
            graphics.DrawString($"Phone: {order.PhoneModel}", font, brush, leftMargin, yPos);
            yPos += 12;

            // IMEI
            graphics.DrawString($"IMEI: {order.IMEI}", font, brush, leftMargin, yPos);
            yPos += 12;

            // Status
            graphics.DrawString($"Status: {order.Status}", font, brush, leftMargin, yPos);
            yPos += 12;

            // Date
            graphics.DrawString($"Date: {order.OrderDate:MM/dd/yyyy}", font, brush, leftMargin, yPos);
        }

        private void PrintReceiptContent(PrintPageEventArgs e, RepairOrder order)
        {
            var graphics = e.Graphics;
            var headerFont = new Font("Arial", 12, FontStyle.Bold);
            var normalFont = new Font("Arial", 10, FontStyle.Regular);
            var smallFont = new Font("Arial", 8, FontStyle.Regular);
            var brush = Brushes.Black;
            float yPos = 10;
            float leftMargin = 10;
            float centerX = e.PageBounds.Width / 2;

            // Header - Company Info
            var companySize = graphics.MeasureString(_companyName, headerFont);
            graphics.DrawString(_companyName, headerFont, brush, centerX - companySize.Width / 2, yPos);
            yPos += 20;

            if (!string.IsNullOrEmpty(_companyAddress))
            {
                var addressSize = graphics.MeasureString(_companyAddress, smallFont);
                graphics.DrawString(_companyAddress, smallFont, brush, centerX - addressSize.Width / 2, yPos);
                yPos += 15;
            }

            if (!string.IsNullOrEmpty(_companyPhone))
            {
                var phoneSize = graphics.MeasureString(_companyPhone, smallFont);
                graphics.DrawString(_companyPhone, smallFont, brush, centerX - phoneSize.Width / 2, yPos);
                yPos += 15;
            }

            // Separator line
            graphics.DrawLine(Pens.Black, leftMargin, yPos, e.PageBounds.Width - leftMargin, yPos);
            yPos += 10;

            // Receipt title
            var receiptTitle = "REPAIR ORDER RECEIPT";
            var titleSize = graphics.MeasureString(receiptTitle, headerFont);
            graphics.DrawString(receiptTitle, headerFont, brush, centerX - titleSize.Width / 2, yPos);
            yPos += 25;

            // Order details
            graphics.DrawString($"Order Number: {order.OrderNumber}", normalFont, brush, leftMargin, yPos);
            yPos += 15;

            graphics.DrawString($"Date: {order.OrderDate:MM/dd/yyyy HH:mm}", normalFont, brush, leftMargin, yPos);
            yPos += 15;

            graphics.DrawString($"Customer: {order.Customer?.FullName ?? "Unknown"}", normalFont, brush, leftMargin, yPos);
            yPos += 15;

            if (!string.IsNullOrEmpty(order.Customer?.PhoneNumber))
            {
                graphics.DrawString($"Phone: {order.Customer.PhoneNumber}", normalFont, brush, leftMargin, yPos);
                yPos += 15;
            }

            graphics.DrawString($"Device: {order.PhoneModel}", normalFont, brush, leftMargin, yPos);
            yPos += 15;

            graphics.DrawString($"IMEI: {order.IMEI}", normalFont, brush, leftMargin, yPos);
            yPos += 15;

            graphics.DrawString($"Problem: {order.ProblemDescription}", normalFont, brush, leftMargin, yPos);
            yPos += 20;

            // Separator line
            graphics.DrawLine(Pens.Black, leftMargin, yPos, e.PageBounds.Width - leftMargin, yPos);
            yPos += 10;

            // Parts and services
            graphics.DrawString("PARTS & SERVICES", normalFont, brush, leftMargin, yPos);
            yPos += 15;

            graphics.DrawLine(Pens.Black, leftMargin, yPos, e.PageBounds.Width - leftMargin, yPos);
            yPos += 5;

            // Column headers
            graphics.DrawString("Item", smallFont, brush, leftMargin, yPos);
            graphics.DrawString("Qty", smallFont, brush, leftMargin + 150, yPos);
            graphics.DrawString("Price", smallFont, brush, leftMargin + 200, yPos);
            graphics.DrawString("Total", smallFont, brush, leftMargin + 250, yPos);
            yPos += 15;

            graphics.DrawLine(Pens.Black, leftMargin, yPos, e.PageBounds.Width - leftMargin, yPos);
            yPos += 5;

            // Parts
            foreach (var part in order.OrderParts)
            {
                graphics.DrawString(part.Part?.PartName ?? "Unknown Part", smallFont, brush, leftMargin, yPos);
                graphics.DrawString(part.Quantity.ToString(), smallFont, brush, leftMargin + 150, yPos);
                graphics.DrawString(part.UnitPrice.ToString("C"), smallFont, brush, leftMargin + 200, yPos);
                graphics.DrawString(part.TotalPrice.ToString("C"), smallFont, brush, leftMargin + 250, yPos);
                yPos += 12;
            }

            // Labor
            if (order.LaborCost > 0)
            {
                graphics.DrawString("Labor", smallFont, brush, leftMargin, yPos);
                graphics.DrawString("1", smallFont, brush, leftMargin + 150, yPos);
                graphics.DrawString(order.LaborCost.ToString("C"), smallFont, brush, leftMargin + 200, yPos);
                graphics.DrawString(order.LaborCost.ToString("C"), smallFont, brush, leftMargin + 250, yPos);
                yPos += 12;
            }

            yPos += 10;
            graphics.DrawLine(Pens.Black, leftMargin, yPos, e.PageBounds.Width - leftMargin, yPos);
            yPos += 10;

            // Total
            graphics.DrawString($"TOTAL: {order.TotalAmount:C}", headerFont, brush, leftMargin + 150, yPos);
            yPos += 25;

            // Status and technician
            graphics.DrawString($"Status: {order.Status}", normalFont, brush, leftMargin, yPos);
            yPos += 15;

            if (!string.IsNullOrEmpty(order.TechnicianName))
            {
                graphics.DrawString($"Technician: {order.TechnicianName}", normalFont, brush, leftMargin, yPos);
                yPos += 15;
            }

            // Warranty info
            if (order.IsWarranty)
            {
                yPos += 10;
                graphics.DrawLine(Pens.Black, leftMargin, yPos, e.PageBounds.Width - leftMargin, yPos);
                yPos += 10;
                graphics.DrawString($"WARRANTY: {order.WarrantyDays} days from completion", normalFont, brush, leftMargin, yPos);
                yPos += 15;
            }

            // Footer
            yPos += 20;
            var footerText = "Thank you for your business!";
            var footerSize = graphics.MeasureString(footerText, normalFont);
            graphics.DrawString(footerText, normalFont, brush, centerX - footerSize.Width / 2, yPos);
        }

        // ESC/POS Commands for direct thermal printer communication
        public void PrintStickerESCPOS(RepairOrder order)
        {
            try
            {
                var escPos = new StringBuilder();
                
                // Initialize printer
                escPos.Append((char)27); // ESC
                escPos.Append("@"); // Initialize
                
                // Set font size
                escPos.Append((char)27);
                escPos.Append("!");
                escPos.Append((char)0); // Normal size
                
                // Center align
                escPos.Append((char)27);
                escPos.Append("a");
                escPos.Append((char)1);
                
                // Company name
                escPos.AppendLine(_companyName);
                
                // Left align
                escPos.Append((char)27);
                escPos.Append("a");
                escPos.Append((char)0);
                
                escPos.AppendLine($"Order: {order.OrderNumber}");
                escPos.AppendLine($"Customer: {order.Customer?.FullName ?? "Unknown"}");
                escPos.AppendLine($"Phone: {order.PhoneModel}");
                escPos.AppendLine($"IMEI: {order.IMEI}");
                escPos.AppendLine($"Status: {order.Status}");
                escPos.AppendLine($"Date: {order.OrderDate:MM/dd/yyyy}");
                
                // Cut paper
                escPos.Append((char)29);
                escPos.Append("V");
                escPos.Append((char)1);
                
                // Send to printer
                SendToPrinter(_thermalPrinterName, escPos.ToString());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error printing sticker with ESC/POS: {ex.Message}", "Print Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        [DllImport("winspool.drv", CharSet = CharSet.Auto, SetLastError = true)]
        public static extern bool OpenPrinter(string pPrinterName, out IntPtr phPrinter, IntPtr pDefault);

        [DllImport("winspool.drv", CharSet = CharSet.Auto, SetLastError = true)]
        public static extern bool ClosePrinter(IntPtr hPrinter);

        [DllImport("winspool.drv", CharSet = CharSet.Auto, SetLastError = true)]
        public static extern bool StartDocPrinter(IntPtr hPrinter, int level, [In, MarshalAs(UnmanagedType.LPStruct)] DOCINFOA di);

        [DllImport("winspool.drv", CharSet = CharSet.Auto, SetLastError = true)]
        public static extern bool EndDocPrinter(IntPtr hPrinter);

        [DllImport("winspool.drv", CharSet = CharSet.Auto, SetLastError = true)]
        public static extern bool StartPagePrinter(IntPtr hPrinter);

        [DllImport("winspool.drv", CharSet = CharSet.Auto, SetLastError = true)]
        public static extern bool EndPagePrinter(IntPtr hPrinter);

        [DllImport("winspool.drv", CharSet = CharSet.Auto, SetLastError = true)]
        public static extern bool WritePrinter(IntPtr hPrinter, IntPtr pBytes, int dwCount, out int dwWritten);

        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Ansi)]
        public class DOCINFOA
        {
            [MarshalAs(UnmanagedType.LPStr)]
            public string pDocName;
            [MarshalAs(UnmanagedType.LPStr)]
            public string pOutputFile;
            [MarshalAs(UnmanagedType.LPStr)]
            public string pDataType;
        }

        private void SendToPrinter(string printerName, string data)
        {
            IntPtr hPrinter = IntPtr.Zero;
            DOCINFOA di = new DOCINFOA();
            di.pDocName = "Mobile Repair Shop";
            di.pDataType = "RAW";

            if (OpenPrinter(printerName, out hPrinter, IntPtr.Zero))
            {
                if (StartDocPrinter(hPrinter, 1, di))
                {
                    if (StartPagePrinter(hPrinter))
                    {
                        byte[] bytes = Encoding.UTF8.GetBytes(data);
                        IntPtr pBytes = Marshal.AllocHGlobal(bytes.Length);
                        Marshal.Copy(bytes, 0, pBytes, bytes.Length);
                        
                        int dwWritten;
                        WritePrinter(hPrinter, pBytes, bytes.Length, out dwWritten);
                        
                        Marshal.FreeHGlobal(pBytes);
                        EndPagePrinter(hPrinter);
                    }
                    EndDocPrinter(hPrinter);
                }
                ClosePrinter(hPrinter);
            }
        }
    }
}
