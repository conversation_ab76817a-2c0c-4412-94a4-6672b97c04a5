using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using MobileRepairShop.Models;

namespace MobileRepairShop.DataAccess
{
    public class CustomerRepository
    {
        public List<Customer> GetAllCustomers()
        {
            var customers = new List<Customer>();
            string sql = "SELECT CustomerID, FirstName, LastName, PhoneNumber, Email, Address, CreatedDate, UpdatedDate FROM Customers ORDER BY LastName, FirstName";

            using (var connection = DatabaseConnection.GetConnection())
            {
                connection.Open();
                using (var reader = DatabaseConnection.ExecuteReader(sql, connection))
                {
                    while (reader.Read())
                    {
                        customers.Add(MapReaderToCustomer(reader));
                    }
                }
            }
            return customers;
        }

        public Customer GetCustomerById(int customerId)
        {
            string sql = "SELECT CustomerID, FirstName, LastName, PhoneNumber, Email, Address, CreatedDate, UpdatedDate FROM Customers WHERE CustomerID = @CustomerID";
            
            using (var connection = DatabaseConnection.GetConnection())
            {
                connection.Open();
                using (var reader = DatabaseConnection.ExecuteReader(sql, connection, new SqlParameter("@CustomerID", customerId)))
                {
                    if (reader.Read())
                    {
                        return MapReaderToCustomer(reader);
                    }
                }
            }
            return null;
        }

        public List<Customer> SearchCustomers(string searchTerm)
        {
            var customers = new List<Customer>();
            string sql = @"SELECT CustomerID, FirstName, LastName, PhoneNumber, Email, Address, CreatedDate, UpdatedDate 
                          FROM Customers 
                          WHERE FirstName LIKE @SearchTerm OR LastName LIKE @SearchTerm OR PhoneNumber LIKE @SearchTerm OR Email LIKE @SearchTerm
                          ORDER BY LastName, FirstName";

            using (var connection = DatabaseConnection.GetConnection())
            {
                connection.Open();
                using (var reader = DatabaseConnection.ExecuteReader(sql, connection, new SqlParameter("@SearchTerm", $"%{searchTerm}%")))
                {
                    while (reader.Read())
                    {
                        customers.Add(MapReaderToCustomer(reader));
                    }
                }
            }
            return customers;
        }

        public int AddCustomer(Customer customer)
        {
            string sql = @"INSERT INTO Customers (FirstName, LastName, PhoneNumber, Email, Address, CreatedDate, UpdatedDate) 
                          VALUES (@FirstName, @LastName, @PhoneNumber, @Email, @Address, @CreatedDate, @UpdatedDate);
                          SELECT SCOPE_IDENTITY();";

            var parameters = new[]
            {
                new SqlParameter("@FirstName", customer.FirstName ?? ""),
                new SqlParameter("@LastName", customer.LastName ?? ""),
                new SqlParameter("@PhoneNumber", customer.PhoneNumber ?? ""),
                new SqlParameter("@Email", customer.Email ?? ""),
                new SqlParameter("@Address", customer.Address ?? ""),
                new SqlParameter("@CreatedDate", customer.CreatedDate),
                new SqlParameter("@UpdatedDate", customer.UpdatedDate)
            };

            var result = DatabaseConnection.ExecuteScalar(sql, parameters);
            return Convert.ToInt32(result);
        }

        public void UpdateCustomer(Customer customer)
        {
            string sql = @"UPDATE Customers SET 
                          FirstName = @FirstName, LastName = @LastName, PhoneNumber = @PhoneNumber, 
                          Email = @Email, Address = @Address, UpdatedDate = @UpdatedDate 
                          WHERE CustomerID = @CustomerID";

            var parameters = new[]
            {
                new SqlParameter("@CustomerID", customer.CustomerID),
                new SqlParameter("@FirstName", customer.FirstName ?? ""),
                new SqlParameter("@LastName", customer.LastName ?? ""),
                new SqlParameter("@PhoneNumber", customer.PhoneNumber ?? ""),
                new SqlParameter("@Email", customer.Email ?? ""),
                new SqlParameter("@Address", customer.Address ?? ""),
                new SqlParameter("@UpdatedDate", DateTime.Now)
            };

            DatabaseConnection.ExecuteNonQuery(sql, parameters);
        }

        public void DeleteCustomer(int customerId)
        {
            string sql = "DELETE FROM Customers WHERE CustomerID = @CustomerID";
            DatabaseConnection.ExecuteNonQuery(sql, new SqlParameter("@CustomerID", customerId));
        }

        private Customer MapReaderToCustomer(SqlDataReader reader)
        {
            return new Customer
            {
                CustomerID = reader.GetInt32("CustomerID"),
                FirstName = reader.IsDBNull(reader.GetOrdinal("FirstName")) ? "" : reader.GetString("FirstName"),
                LastName = reader.IsDBNull(reader.GetOrdinal("LastName")) ? "" : reader.GetString("LastName"),
                PhoneNumber = reader.IsDBNull(reader.GetOrdinal("PhoneNumber")) ? "" : reader.GetString("PhoneNumber"),
                Email = reader.IsDBNull(reader.GetOrdinal("Email")) ? "" : reader.GetString("Email"),
                Address = reader.IsDBNull(reader.GetOrdinal("Address")) ? "" : reader.GetString("Address"),
                CreatedDate = reader.GetDateTime("CreatedDate"),
                UpdatedDate = reader.GetDateTime("UpdatedDate")
            };
        }
    }
}
