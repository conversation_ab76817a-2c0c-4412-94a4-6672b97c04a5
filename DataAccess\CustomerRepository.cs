using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using MobileRepairShop.Models;

namespace MobileRepairShop.DataAccess
{
    public class CustomerRepository
    {
        public List<Customer> GetAllCustomers()
        {
            // Use mock data for demo
            return MockDataService.GetAllCustomers();
        }

        public Customer GetCustomerById(int customerId)
        {
            return MockDataService.GetCustomerById(customerId);
        }

        public List<Customer> SearchCustomers(string searchTerm)
        {
            return MockDataService.SearchCustomers(searchTerm);
        }

        public int AddCustomer(Customer customer)
        {
            return MockDataService.AddCustomer(customer);
        }

        public void UpdateCustomer(Customer customer)
        {
            MockDataService.UpdateCustomer(customer);
        }

        public void DeleteCustomer(int customerId)
        {
            MockDataService.DeleteCustomer(customerId);
        }


    }
}
