<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <startup>
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.6.2" />
    </startup>
    
    <connectionStrings>
        <add name="MobileRepairShop" 
             connectionString="Data Source=.\SQLEXPRESS;Initial Catalog=MobileRepairShop;Integrated Security=True;Connect Timeout=30;Encrypt=False;TrustServerCertificate=True;ApplicationIntent=ReadWrite;MultiSubnetFailover=False" 
             providerName="System.Data.SqlClient" />
    </connectionStrings>
    
    <appSettings>
        <add key="CompanyName" value="Mobile Repair Shop" />
        <add key="CompanyAddress" value="123 Main Street, City, State 12345" />
        <add key="CompanyPhone" value="+****************" />
        <add key="CompanyEmail" value="<EMAIL>" />
        <add key="DefaultTechnician" value="Tech Support" />
        <add key="DefaultWarrantyDays" value="30" />
        <add key="ThermalPrinterName" value="Generic / Text Only" />
        <add key="ReceiptPrinterName" value="Generic / Text Only" />
    </appSettings>
</configuration>
