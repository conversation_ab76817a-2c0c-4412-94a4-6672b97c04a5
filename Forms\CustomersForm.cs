using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using MobileRepairShop.DataAccess;
using MobileRepairShop.Models;

namespace MobileRepairShop.Forms
{
    public partial class CustomersForm : Form
    {
        private readonly CustomerRepository _customerRepository;
        private DataGridView dgvCustomers;
        private TextBox txtSearch;
        private Button btnAdd;
        private Button btnEdit;
        private Button btnDelete;
        private Button btnRefresh;

        public CustomersForm()
        {
            _customerRepository = new CustomerRepository();
            InitializeComponent();
            SetupCustomersForm();
            LoadCustomers();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // CustomersForm
            this.AutoScaleDimensions = new SizeF(8F, 16F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.BackColor = Color.FromArgb(240, 240, 240);
            this.ClientSize = new Size(1000, 700);
            this.Name = "CustomersForm";
            this.Text = "Customers";
            
            this.ResumeLayout(false);
        }

        private void SetupCustomersForm()
        {
            // Title
            var titleLabel = new Label
            {
                Text = "Customer Management",
                Font = new Font("Segoe UI", 24, FontStyle.Bold),
                ForeColor = Color.FromArgb(51, 51, 76),
                AutoSize = true,
                Location = new Point(20, 20)
            };

            // Search Panel
            var searchPanel = new Panel
            {
                Location = new Point(20, 70),
                Size = new Size(960, 50),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var searchLabel = new Label
            {
                Text = "Search:",
                Font = new Font("Segoe UI", 10),
                Location = new Point(10, 15),
                AutoSize = true
            };

            txtSearch = new TextBox
            {
                Location = new Point(70, 12),
                Size = new Size(300, 25),
                Font = new Font("Segoe UI", 10)
            };
            txtSearch.TextChanged += TxtSearch_TextChanged;

            btnAdd = CreateButton("Add Customer", new Point(400, 10), Color.FromArgb(40, 167, 69));
            btnEdit = CreateButton("Edit", new Point(520, 10), Color.FromArgb(0, 126, 249));
            btnDelete = CreateButton("Delete", new Point(620, 10), Color.FromArgb(220, 53, 69));
            btnRefresh = CreateButton("Refresh", new Point(720, 10), Color.FromArgb(108, 117, 125));

            btnAdd.Click += BtnAdd_Click;
            btnEdit.Click += BtnEdit_Click;
            btnDelete.Click += BtnDelete_Click;
            btnRefresh.Click += BtnRefresh_Click;

            searchPanel.Controls.Add(searchLabel);
            searchPanel.Controls.Add(txtSearch);
            searchPanel.Controls.Add(btnAdd);
            searchPanel.Controls.Add(btnEdit);
            searchPanel.Controls.Add(btnDelete);
            searchPanel.Controls.Add(btnRefresh);

            // Customers DataGridView
            dgvCustomers = new DataGridView
            {
                Location = new Point(20, 140),
                Size = new Size(960, 540),
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };

            StyleDataGridView(dgvCustomers);

            // Add controls to form
            this.Controls.Add(titleLabel);
            this.Controls.Add(searchPanel);
            this.Controls.Add(dgvCustomers);
        }

        private Button CreateButton(string text, Point location, Color backColor)
        {
            return new Button
            {
                Text = text,
                Location = location,
                Size = new Size(100, 30),
                BackColor = backColor,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9),
                Cursor = Cursors.Hand,
                FlatAppearance = { BorderSize = 0 }
            };
        }

        private void StyleDataGridView(DataGridView dgv)
        {
            dgv.EnableHeadersVisualStyles = false;
            dgv.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(51, 51, 76);
            dgv.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgv.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10, FontStyle.Bold);
            dgv.DefaultCellStyle.SelectionBackColor = Color.FromArgb(0, 126, 249);
            dgv.DefaultCellStyle.SelectionForeColor = Color.White;
            dgv.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);
            dgv.RowHeadersVisible = false;
        }

        private void LoadCustomers()
        {
            try
            {
                var customers = _customerRepository.GetAllCustomers().Select(c => new
                {
                    CustomerID = c.CustomerID,
                    FirstName = c.FirstName,
                    LastName = c.LastName,
                    PhoneNumber = c.PhoneNumber,
                    Email = c.Email,
                    Address = c.Address,
                    CreatedDate = c.CreatedDate.ToString("MM/dd/yyyy")
                }).ToList();

                dgvCustomers.DataSource = customers;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading customers: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtSearch.Text))
            {
                LoadCustomers();
            }
            else
            {
                SearchCustomers(txtSearch.Text);
            }
        }

        private void SearchCustomers(string searchTerm)
        {
            try
            {
                var customers = _customerRepository.SearchCustomers(searchTerm).Select(c => new
                {
                    CustomerID = c.CustomerID,
                    FirstName = c.FirstName,
                    LastName = c.LastName,
                    PhoneNumber = c.PhoneNumber,
                    Email = c.Email,
                    Address = c.Address,
                    CreatedDate = c.CreatedDate.ToString("MM/dd/yyyy")
                }).ToList();

                dgvCustomers.DataSource = customers;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error searching customers: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            var addCustomerForm = new AddEditCustomerForm();
            if (addCustomerForm.ShowDialog() == DialogResult.OK)
            {
                LoadCustomers();
            }
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            if (dgvCustomers.SelectedRows.Count > 0)
            {
                var customerId = Convert.ToInt32(dgvCustomers.SelectedRows[0].Cells["CustomerID"].Value);
                var customer = _customerRepository.GetCustomerById(customerId);
                
                var editCustomerForm = new AddEditCustomerForm(customer);
                if (editCustomerForm.ShowDialog() == DialogResult.OK)
                {
                    LoadCustomers();
                }
            }
            else
            {
                MessageBox.Show("Please select a customer to edit.", "No Selection", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            if (dgvCustomers.SelectedRows.Count > 0)
            {
                var customerName = $"{dgvCustomers.SelectedRows[0].Cells["FirstName"].Value} {dgvCustomers.SelectedRows[0].Cells["LastName"].Value}";
                var result = MessageBox.Show($"Are you sure you want to delete '{customerName}'?", 
                    "Confirm Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                
                if (result == DialogResult.Yes)
                {
                    try
                    {
                        var customerId = Convert.ToInt32(dgvCustomers.SelectedRows[0].Cells["CustomerID"].Value);
                        _customerRepository.DeleteCustomer(customerId);
                        LoadCustomers();
                        MessageBox.Show("Customer deleted successfully.", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Error deleting customer: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("Please select a customer to delete.", "No Selection", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            txtSearch.Text = "";
            LoadCustomers();
        }
    }
}
