{"version": 3, "targets": {".NETFramework,Version=v4.8": {"Microsoft.NETFramework.ReferenceAssemblies/1.0.3": {"type": "package", "dependencies": {"Microsoft.NETFramework.ReferenceAssemblies.net48": "1.0.3"}}, "Microsoft.NETFramework.ReferenceAssemblies.net48/1.0.3": {"type": "package", "build": {"build/Microsoft.NETFramework.ReferenceAssemblies.net48.targets": {}}}, "System.Configuration.ConfigurationManager/8.0.0": {"type": "package", "frameworkAssemblies": ["System.Configuration"], "compile": {"lib/net462/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Data.SqlClient/4.8.6": {"type": "package", "frameworkAssemblies": ["System.Data", "System.Xml", "mscorlib"], "compile": {"ref/net461/System.Data.SqlClient.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Data.SqlClient.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard2.0/System.Data.SqlClient.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/net461/System.Data.SqlClient.dll": {"assetType": "runtime", "rid": "win"}}}}, ".NETFramework,Version=v4.8/win-x86": {"Microsoft.NETFramework.ReferenceAssemblies/1.0.3": {"type": "package", "dependencies": {"Microsoft.NETFramework.ReferenceAssemblies.net48": "1.0.3"}}, "Microsoft.NETFramework.ReferenceAssemblies.net48/1.0.3": {"type": "package", "build": {"build/Microsoft.NETFramework.ReferenceAssemblies.net48.targets": {}}}, "System.Configuration.ConfigurationManager/8.0.0": {"type": "package", "frameworkAssemblies": ["System.Configuration"], "compile": {"lib/net462/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Data.SqlClient/4.8.6": {"type": "package", "frameworkAssemblies": ["System.Data", "System.Xml", "mscorlib"], "compile": {"ref/net461/System.Data.SqlClient.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/net461/System.Data.SqlClient.dll": {"related": ".xml"}}}}}, "libraries": {"Microsoft.NETFramework.ReferenceAssemblies/1.0.3": {"sha512": "vUc9Npcs14QsyOD01tnv/m8sQUnGTGOw1BCmKcv77LBJY7OxhJ+zJF7UD/sCL3lYNFuqmQEVlkfS4Quif6FyYg==", "type": "package", "path": "microsoft.netframework.referenceassemblies/1.0.3", "files": [".nupkg.metadata", ".signature.p7s", "microsoft.netframework.referenceassemblies.1.0.3.nupkg.sha512", "microsoft.netframework.referenceassemblies.nuspec"]}, "Microsoft.NETFramework.ReferenceAssemblies.net48/1.0.3": {"sha512": "zMk4D+9zyiEWByyQ7oPImPN/Jhpj166Ky0Nlla4eXlNL8hI/BtSJsgR8Inldd4NNpIAH3oh8yym0W2DrhXdSLQ==", "type": "package", "path": "microsoft.netframework.referenceassemblies.net48/1.0.3", "files": [".nupkg.metadata", ".signature.p7s", "build/.NETFramework/v4.8/Accessibility.dll", "build/.NETFramework/v4.8/Accessibility.xml", "build/.NETFramework/v4.8/CustomMarshalers.dll", "build/.NETFramework/v4.8/CustomMarshalers.xml", "build/.NETFramework/v4.8/Facades/Microsoft.Win32.Primitives.dll", "build/.NETFramework/v4.8/Facades/System.AppContext.dll", "build/.NETFramework/v4.8/Facades/System.Collections.Concurrent.dll", "build/.NETFramework/v4.8/Facades/System.Collections.NonGeneric.dll", "build/.NETFramework/v4.8/Facades/System.Collections.Specialized.dll", "build/.NETFramework/v4.8/Facades/System.Collections.dll", "build/.NETFramework/v4.8/Facades/System.ComponentModel.Annotations.dll", "build/.NETFramework/v4.8/Facades/System.ComponentModel.EventBasedAsync.dll", "build/.NETFramework/v4.8/Facades/System.ComponentModel.Primitives.dll", "build/.NETFramework/v4.8/Facades/System.ComponentModel.TypeConverter.dll", "build/.NETFramework/v4.8/Facades/System.ComponentModel.dll", "build/.NETFramework/v4.8/Facades/System.Console.dll", "build/.NETFramework/v4.8/Facades/System.Data.Common.dll", "build/.NETFramework/v4.8/Facades/System.Diagnostics.Contracts.dll", "build/.NETFramework/v4.8/Facades/System.Diagnostics.Debug.dll", "build/.NETFramework/v4.8/Facades/System.Diagnostics.FileVersionInfo.dll", "build/.NETFramework/v4.8/Facades/System.Diagnostics.Process.dll", "build/.NETFramework/v4.8/Facades/System.Diagnostics.StackTrace.dll", "build/.NETFramework/v4.8/Facades/System.Diagnostics.TextWriterTraceListener.dll", "build/.NETFramework/v4.8/Facades/System.Diagnostics.Tools.dll", "build/.NETFramework/v4.8/Facades/System.Diagnostics.TraceSource.dll", "build/.NETFramework/v4.8/Facades/System.Drawing.Primitives.dll", "build/.NETFramework/v4.8/Facades/System.Dynamic.Runtime.dll", "build/.NETFramework/v4.8/Facades/System.Globalization.Calendars.dll", "build/.NETFramework/v4.8/Facades/System.Globalization.Extensions.dll", "build/.NETFramework/v4.8/Facades/System.Globalization.dll", "build/.NETFramework/v4.8/Facades/System.IO.Compression.ZipFile.dll", "build/.NETFramework/v4.8/Facades/System.IO.FileSystem.DriveInfo.dll", "build/.NETFramework/v4.8/Facades/System.IO.FileSystem.Primitives.dll", "build/.NETFramework/v4.8/Facades/System.IO.FileSystem.Watcher.dll", "build/.NETFramework/v4.8/Facades/System.IO.FileSystem.dll", "build/.NETFramework/v4.8/Facades/System.IO.IsolatedStorage.dll", "build/.NETFramework/v4.8/Facades/System.IO.MemoryMappedFiles.dll", "build/.NETFramework/v4.8/Facades/System.IO.Pipes.dll", "build/.NETFramework/v4.8/Facades/System.IO.UnmanagedMemoryStream.dll", "build/.NETFramework/v4.8/Facades/System.IO.dll", "build/.NETFramework/v4.8/Facades/System.Linq.Expressions.dll", "build/.NETFramework/v4.8/Facades/System.Linq.Parallel.dll", "build/.NETFramework/v4.8/Facades/System.Linq.Queryable.dll", "build/.NETFramework/v4.8/Facades/System.Linq.dll", "build/.NETFramework/v4.8/Facades/System.Net.Http.Rtc.dll", "build/.NETFramework/v4.8/Facades/System.Net.NameResolution.dll", "build/.NETFramework/v4.8/Facades/System.Net.NetworkInformation.dll", "build/.NETFramework/v4.8/Facades/System.Net.Ping.dll", "build/.NETFramework/v4.8/Facades/System.Net.Primitives.dll", "build/.NETFramework/v4.8/Facades/System.Net.Requests.dll", "build/.NETFramework/v4.8/Facades/System.Net.Security.dll", "build/.NETFramework/v4.8/Facades/System.Net.Sockets.dll", "build/.NETFramework/v4.8/Facades/System.Net.WebHeaderCollection.dll", "build/.NETFramework/v4.8/Facades/System.Net.WebSockets.Client.dll", "build/.NETFramework/v4.8/Facades/System.Net.WebSockets.dll", "build/.NETFramework/v4.8/Facades/System.ObjectModel.dll", "build/.NETFramework/v4.8/Facades/System.Reflection.Emit.ILGeneration.dll", "build/.NETFramework/v4.8/Facades/System.Reflection.Emit.Lightweight.dll", "build/.NETFramework/v4.8/Facades/System.Reflection.Emit.dll", "build/.NETFramework/v4.8/Facades/System.Reflection.Extensions.dll", "build/.NETFramework/v4.8/Facades/System.Reflection.Primitives.dll", "build/.NETFramework/v4.8/Facades/System.Reflection.dll", "build/.NETFramework/v4.8/Facades/System.Resources.Reader.dll", "build/.NETFramework/v4.8/Facades/System.Resources.ResourceManager.dll", "build/.NETFramework/v4.8/Facades/System.Resources.Writer.dll", "build/.NETFramework/v4.8/Facades/System.Runtime.CompilerServices.VisualC.dll", "build/.NETFramework/v4.8/Facades/System.Runtime.Extensions.dll", "build/.NETFramework/v4.8/Facades/System.Runtime.Handles.dll", "build/.NETFramework/v4.8/Facades/System.Runtime.InteropServices.RuntimeInformation.dll", "build/.NETFramework/v4.8/Facades/System.Runtime.InteropServices.WindowsRuntime.dll", "build/.NETFramework/v4.8/Facades/System.Runtime.InteropServices.dll", "build/.NETFramework/v4.8/Facades/System.Runtime.Numerics.dll", "build/.NETFramework/v4.8/Facades/System.Runtime.Serialization.Formatters.dll", "build/.NETFramework/v4.8/Facades/System.Runtime.Serialization.Json.dll", "build/.NETFramework/v4.8/Facades/System.Runtime.Serialization.Primitives.dll", "build/.NETFramework/v4.8/Facades/System.Runtime.Serialization.Xml.dll", "build/.NETFramework/v4.8/Facades/System.Runtime.dll", "build/.NETFramework/v4.8/Facades/System.Security.Claims.dll", "build/.NETFramework/v4.8/Facades/System.Security.Cryptography.Algorithms.dll", "build/.NETFramework/v4.8/Facades/System.Security.Cryptography.Csp.dll", "build/.NETFramework/v4.8/Facades/System.Security.Cryptography.Encoding.dll", "build/.NETFramework/v4.8/Facades/System.Security.Cryptography.Primitives.dll", "build/.NETFramework/v4.8/Facades/System.Security.Cryptography.X509Certificates.dll", "build/.NETFramework/v4.8/Facades/System.Security.Principal.dll", "build/.NETFramework/v4.8/Facades/System.Security.SecureString.dll", "build/.NETFramework/v4.8/Facades/System.ServiceModel.Duplex.dll", "build/.NETFramework/v4.8/Facades/System.ServiceModel.Http.dll", "build/.NETFramework/v4.8/Facades/System.ServiceModel.NetTcp.dll", "build/.NETFramework/v4.8/Facades/System.ServiceModel.Primitives.dll", "build/.NETFramework/v4.8/Facades/System.ServiceModel.Security.dll", "build/.NETFramework/v4.8/Facades/System.Text.Encoding.Extensions.dll", "build/.NETFramework/v4.8/Facades/System.Text.Encoding.dll", "build/.NETFramework/v4.8/Facades/System.Text.RegularExpressions.dll", "build/.NETFramework/v4.8/Facades/System.Threading.Overlapped.dll", "build/.NETFramework/v4.8/Facades/System.Threading.Tasks.Parallel.dll", "build/.NETFramework/v4.8/Facades/System.Threading.Tasks.dll", "build/.NETFramework/v4.8/Facades/System.Threading.Thread.dll", "build/.NETFramework/v4.8/Facades/System.Threading.ThreadPool.dll", "build/.NETFramework/v4.8/Facades/System.Threading.Timer.dll", "build/.NETFramework/v4.8/Facades/System.Threading.dll", "build/.NETFramework/v4.8/Facades/System.ValueTuple.dll", "build/.NETFramework/v4.8/Facades/System.Xml.ReaderWriter.dll", "build/.NETFramework/v4.8/Facades/System.Xml.XDocument.dll", "build/.NETFramework/v4.8/Facades/System.Xml.XPath.XDocument.dll", "build/.NETFramework/v4.8/Facades/System.Xml.XPath.dll", "build/.NETFramework/v4.8/Facades/System.Xml.XmlDocument.dll", "build/.NETFramework/v4.8/Facades/System.Xml.XmlSerializer.dll", "build/.NETFramework/v4.8/Facades/netstandard.dll", "build/.NETFramework/v4.8/ISymWrapper.dll", "build/.NETFramework/v4.8/ISymWrapper.xml", "build/.NETFramework/v4.8/Microsoft.Activities.Build.dll", "build/.NETFramework/v4.8/Microsoft.Activities.Build.xml", "build/.NETFramework/v4.8/Microsoft.Build.Conversion.v4.0.dll", "build/.NETFramework/v4.8/Microsoft.Build.Conversion.v4.0.xml", "build/.NETFramework/v4.8/Microsoft.Build.Engine.dll", "build/.NETFramework/v4.8/Microsoft.Build.Engine.xml", "build/.NETFramework/v4.8/Microsoft.Build.Framework.dll", "build/.NETFramework/v4.8/Microsoft.Build.Framework.xml", "build/.NETFramework/v4.8/Microsoft.Build.Tasks.v4.0.dll", "build/.NETFramework/v4.8/Microsoft.Build.Tasks.v4.0.xml", "build/.NETFramework/v4.8/Microsoft.Build.Utilities.v4.0.dll", "build/.NETFramework/v4.8/Microsoft.Build.Utilities.v4.0.xml", "build/.NETFramework/v4.8/Microsoft.Build.dll", "build/.NETFramework/v4.8/Microsoft.Build.xml", "build/.NETFramework/v4.8/Microsoft.CSharp.dll", "build/.NETFramework/v4.8/Microsoft.CSharp.xml", "build/.NETFramework/v4.8/Microsoft.JScript.dll", "build/.NETFramework/v4.8/Microsoft.JScript.xml", "build/.NETFramework/v4.8/Microsoft.VisualBasic.Compatibility.Data.dll", "build/.NETFramework/v4.8/Microsoft.VisualBasic.Compatibility.Data.xml", "build/.NETFramework/v4.8/Microsoft.VisualBasic.Compatibility.dll", "build/.NETFramework/v4.8/Microsoft.VisualBasic.Compatibility.xml", "build/.NETFramework/v4.8/Microsoft.VisualBasic.dll", "build/.NETFramework/v4.8/Microsoft.VisualBasic.xml", "build/.NETFramework/v4.8/Microsoft.VisualC.STLCLR.dll", "build/.NETFramework/v4.8/Microsoft.VisualC.STLCLR.xml", "build/.NETFramework/v4.8/Microsoft.VisualC.dll", "build/.NETFramework/v4.8/Microsoft.VisualC.xml", "build/.NETFramework/v4.8/PermissionSets/FullTrust.xml", "build/.NETFramework/v4.8/PermissionSets/Internet.xml", "build/.NETFramework/v4.8/PermissionSets/LocalIntranet.xml", "build/.NETFramework/v4.8/PresentationBuildTasks.dll", "build/.NETFramework/v4.8/PresentationBuildTasks.xml", "build/.NETFramework/v4.8/PresentationCore.dll", "build/.NETFramework/v4.8/PresentationCore.xml", "build/.NETFramework/v4.8/PresentationFramework.Aero.dll", "build/.NETFramework/v4.8/PresentationFramework.Aero.xml", "build/.NETFramework/v4.8/PresentationFramework.Aero2.dll", "build/.NETFramework/v4.8/PresentationFramework.Aero2.xml", "build/.NETFramework/v4.8/PresentationFramework.AeroLite.dll", "build/.NETFramework/v4.8/PresentationFramework.AeroLite.xml", "build/.NETFramework/v4.8/PresentationFramework.Classic.dll", "build/.NETFramework/v4.8/PresentationFramework.Classic.xml", "build/.NETFramework/v4.8/PresentationFramework.Luna.dll", "build/.NETFramework/v4.8/PresentationFramework.Luna.xml", "build/.NETFramework/v4.8/PresentationFramework.Royale.dll", "build/.NETFramework/v4.8/PresentationFramework.Royale.xml", "build/.NETFramework/v4.8/PresentationFramework.dll", "build/.NETFramework/v4.8/PresentationFramework.xml", "build/.NETFramework/v4.8/ReachFramework.dll", "build/.NETFramework/v4.8/ReachFramework.xml", "build/.NETFramework/v4.8/RedistList/FrameworkList.xml", "build/.NETFramework/v4.8/System.Activities.Core.Presentation.dll", "build/.NETFramework/v4.8/System.Activities.Core.Presentation.xml", "build/.NETFramework/v4.8/System.Activities.DurableInstancing.dll", "build/.NETFramework/v4.8/System.Activities.DurableInstancing.xml", "build/.NETFramework/v4.8/System.Activities.Presentation.dll", "build/.NETFramework/v4.8/System.Activities.Presentation.xml", "build/.NETFramework/v4.8/System.Activities.dll", "build/.NETFramework/v4.8/System.Activities.xml", "build/.NETFramework/v4.8/System.AddIn.Contract.dll", "build/.NETFramework/v4.8/System.AddIn.Contract.xml", "build/.NETFramework/v4.8/System.AddIn.dll", "build/.NETFramework/v4.8/System.AddIn.xml", "build/.NETFramework/v4.8/System.ComponentModel.Composition.Registration.dll", "build/.NETFramework/v4.8/System.ComponentModel.Composition.Registration.xml", "build/.NETFramework/v4.8/System.ComponentModel.Composition.dll", "build/.NETFramework/v4.8/System.ComponentModel.Composition.xml", "build/.NETFramework/v4.8/System.ComponentModel.DataAnnotations.dll", "build/.NETFramework/v4.8/System.ComponentModel.DataAnnotations.xml", "build/.NETFramework/v4.8/System.Configuration.Install.dll", "build/.NETFramework/v4.8/System.Configuration.Install.xml", "build/.NETFramework/v4.8/System.Configuration.dll", "build/.NETFramework/v4.8/System.Configuration.xml", "build/.NETFramework/v4.8/System.Core.dll", "build/.NETFramework/v4.8/System.Core.xml", "build/.NETFramework/v4.8/System.Data.DataSetExtensions.dll", "build/.NETFramework/v4.8/System.Data.DataSetExtensions.xml", "build/.NETFramework/v4.8/System.Data.Entity.Design.dll", "build/.NETFramework/v4.8/System.Data.Entity.Design.xml", "build/.NETFramework/v4.8/System.Data.Entity.dll", "build/.NETFramework/v4.8/System.Data.Entity.xml", "build/.NETFramework/v4.8/System.Data.Linq.dll", "build/.NETFramework/v4.8/System.Data.Linq.xml", "build/.NETFramework/v4.8/System.Data.OracleClient.dll", "build/.NETFramework/v4.8/System.Data.OracleClient.xml", "build/.NETFramework/v4.8/System.Data.Services.Client.dll", "build/.NETFramework/v4.8/System.Data.Services.Client.xml", "build/.NETFramework/v4.8/System.Data.Services.Design.dll", "build/.NETFramework/v4.8/System.Data.Services.Design.xml", "build/.NETFramework/v4.8/System.Data.Services.dll", "build/.NETFramework/v4.8/System.Data.Services.xml", "build/.NETFramework/v4.8/System.Data.SqlXml.dll", "build/.NETFramework/v4.8/System.Data.SqlXml.xml", "build/.NETFramework/v4.8/System.Data.dll", "build/.NETFramework/v4.8/System.Data.xml", "build/.NETFramework/v4.8/System.Deployment.dll", "build/.NETFramework/v4.8/System.Deployment.xml", "build/.NETFramework/v4.8/System.Design.dll", "build/.NETFramework/v4.8/System.Design.xml", "build/.NETFramework/v4.8/System.Device.dll", "build/.NETFramework/v4.8/System.Device.xml", "build/.NETFramework/v4.8/System.Diagnostics.Tracing.dll", "build/.NETFramework/v4.8/System.Diagnostics.Tracing.xml", "build/.NETFramework/v4.8/System.DirectoryServices.AccountManagement.dll", "build/.NETFramework/v4.8/System.DirectoryServices.AccountManagement.xml", "build/.NETFramework/v4.8/System.DirectoryServices.Protocols.dll", "build/.NETFramework/v4.8/System.DirectoryServices.Protocols.xml", "build/.NETFramework/v4.8/System.DirectoryServices.dll", "build/.NETFramework/v4.8/System.DirectoryServices.xml", "build/.NETFramework/v4.8/System.Drawing.Design.dll", "build/.NETFramework/v4.8/System.Drawing.Design.xml", "build/.NETFramework/v4.8/System.Drawing.dll", "build/.NETFramework/v4.8/System.Drawing.xml", "build/.NETFramework/v4.8/System.Dynamic.dll", "build/.NETFramework/v4.8/System.EnterpriseServices.Thunk.dll", "build/.NETFramework/v4.8/System.EnterpriseServices.Wrapper.dll", "build/.NETFramework/v4.8/System.EnterpriseServices.dll", "build/.NETFramework/v4.8/System.EnterpriseServices.xml", "build/.NETFramework/v4.8/System.IO.Compression.FileSystem.dll", "build/.NETFramework/v4.8/System.IO.Compression.FileSystem.xml", "build/.NETFramework/v4.8/System.IO.Compression.dll", "build/.NETFramework/v4.8/System.IO.Compression.xml", "build/.NETFramework/v4.8/System.IO.Log.dll", "build/.NETFramework/v4.8/System.IO.Log.xml", "build/.NETFramework/v4.8/System.IdentityModel.Selectors.dll", "build/.NETFramework/v4.8/System.IdentityModel.Selectors.xml", "build/.NETFramework/v4.8/System.IdentityModel.Services.dll", "build/.NETFramework/v4.8/System.IdentityModel.Services.xml", "build/.NETFramework/v4.8/System.IdentityModel.dll", "build/.NETFramework/v4.8/System.IdentityModel.xml", "build/.NETFramework/v4.8/System.Linq.xml", "build/.NETFramework/v4.8/System.Management.Instrumentation.dll", "build/.NETFramework/v4.8/System.Management.Instrumentation.xml", "build/.NETFramework/v4.8/System.Management.dll", "build/.NETFramework/v4.8/System.Management.xml", "build/.NETFramework/v4.8/System.Messaging.dll", "build/.NETFramework/v4.8/System.Messaging.xml", "build/.NETFramework/v4.8/System.Net.Http.WebRequest.dll", "build/.NETFramework/v4.8/System.Net.Http.WebRequest.xml", "build/.NETFramework/v4.8/System.Net.Http.dll", "build/.NETFramework/v4.8/System.Net.Http.xml", "build/.NETFramework/v4.8/System.Net.dll", "build/.NETFramework/v4.8/System.Net.xml", "build/.NETFramework/v4.8/System.Numerics.dll", "build/.NETFramework/v4.8/System.Numerics.xml", "build/.NETFramework/v4.8/System.Printing.dll", "build/.NETFramework/v4.8/System.Printing.xml", "build/.NETFramework/v4.8/System.Reflection.Context.dll", "build/.NETFramework/v4.8/System.Reflection.Context.xml", "build/.NETFramework/v4.8/System.Runtime.Caching.dll", "build/.NETFramework/v4.8/System.Runtime.Caching.xml", "build/.NETFramework/v4.8/System.Runtime.DurableInstancing.dll", "build/.NETFramework/v4.8/System.Runtime.DurableInstancing.xml", "build/.NETFramework/v4.8/System.Runtime.Remoting.dll", "build/.NETFramework/v4.8/System.Runtime.Remoting.xml", "build/.NETFramework/v4.8/System.Runtime.Serialization.Formatters.Soap.dll", "build/.NETFramework/v4.8/System.Runtime.Serialization.Formatters.Soap.xml", "build/.NETFramework/v4.8/System.Runtime.Serialization.dll", "build/.NETFramework/v4.8/System.Runtime.Serialization.xml", "build/.NETFramework/v4.8/System.Security.dll", "build/.NETFramework/v4.8/System.Security.xml", "build/.NETFramework/v4.8/System.ServiceModel.Activation.dll", "build/.NETFramework/v4.8/System.ServiceModel.Activation.xml", "build/.NETFramework/v4.8/System.ServiceModel.Activities.dll", "build/.NETFramework/v4.8/System.ServiceModel.Activities.xml", "build/.NETFramework/v4.8/System.ServiceModel.Channels.dll", "build/.NETFramework/v4.8/System.ServiceModel.Channels.xml", "build/.NETFramework/v4.8/System.ServiceModel.Discovery.dll", "build/.NETFramework/v4.8/System.ServiceModel.Discovery.xml", "build/.NETFramework/v4.8/System.ServiceModel.Routing.dll", "build/.NETFramework/v4.8/System.ServiceModel.Routing.xml", "build/.NETFramework/v4.8/System.ServiceModel.Web.dll", "build/.NETFramework/v4.8/System.ServiceModel.Web.xml", "build/.NETFramework/v4.8/System.ServiceModel.dll", "build/.NETFramework/v4.8/System.ServiceModel.xml", "build/.NETFramework/v4.8/System.ServiceProcess.dll", "build/.NETFramework/v4.8/System.ServiceProcess.xml", "build/.NETFramework/v4.8/System.Speech.dll", "build/.NETFramework/v4.8/System.Speech.xml", "build/.NETFramework/v4.8/System.Threading.Tasks.Dataflow.xml", "build/.NETFramework/v4.8/System.Transactions.dll", "build/.NETFramework/v4.8/System.Transactions.xml", "build/.NETFramework/v4.8/System.Web.Abstractions.dll", "build/.NETFramework/v4.8/System.Web.ApplicationServices.dll", "build/.NETFramework/v4.8/System.Web.ApplicationServices.xml", "build/.NETFramework/v4.8/System.Web.DataVisualization.Design.dll", "build/.NETFramework/v4.8/System.Web.DataVisualization.dll", "build/.NETFramework/v4.8/System.Web.DataVisualization.xml", "build/.NETFramework/v4.8/System.Web.DynamicData.Design.dll", "build/.NETFramework/v4.8/System.Web.DynamicData.Design.xml", "build/.NETFramework/v4.8/System.Web.DynamicData.dll", "build/.NETFramework/v4.8/System.Web.DynamicData.xml", "build/.NETFramework/v4.8/System.Web.Entity.Design.dll", "build/.NETFramework/v4.8/System.Web.Entity.Design.xml", "build/.NETFramework/v4.8/System.Web.Entity.dll", "build/.NETFramework/v4.8/System.Web.Entity.xml", "build/.NETFramework/v4.8/System.Web.Extensions.Design.dll", "build/.NETFramework/v4.8/System.Web.Extensions.Design.xml", "build/.NETFramework/v4.8/System.Web.Extensions.dll", "build/.NETFramework/v4.8/System.Web.Extensions.xml", "build/.NETFramework/v4.8/System.Web.Mobile.dll", "build/.NETFramework/v4.8/System.Web.Mobile.xml", "build/.NETFramework/v4.8/System.Web.RegularExpressions.dll", "build/.NETFramework/v4.8/System.Web.RegularExpressions.xml", "build/.NETFramework/v4.8/System.Web.Routing.dll", "build/.NETFramework/v4.8/System.Web.Services.dll", "build/.NETFramework/v4.8/System.Web.Services.xml", "build/.NETFramework/v4.8/System.Web.dll", "build/.NETFramework/v4.8/System.Web.xml", "build/.NETFramework/v4.8/System.Windows.Controls.Ribbon.dll", "build/.NETFramework/v4.8/System.Windows.Controls.Ribbon.xml", "build/.NETFramework/v4.8/System.Windows.Forms.DataVisualization.Design.dll", "build/.NETFramework/v4.8/System.Windows.Forms.DataVisualization.dll", "build/.NETFramework/v4.8/System.Windows.Forms.DataVisualization.xml", "build/.NETFramework/v4.8/System.Windows.Forms.dll", "build/.NETFramework/v4.8/System.Windows.Forms.xml", "build/.NETFramework/v4.8/System.Windows.Input.Manipulations.dll", "build/.NETFramework/v4.8/System.Windows.Input.Manipulations.xml", "build/.NETFramework/v4.8/System.Windows.Presentation.dll", "build/.NETFramework/v4.8/System.Windows.Presentation.xml", "build/.NETFramework/v4.8/System.Windows.dll", "build/.NETFramework/v4.8/System.Workflow.Activities.dll", "build/.NETFramework/v4.8/System.Workflow.Activities.xml", "build/.NETFramework/v4.8/System.Workflow.ComponentModel.dll", "build/.NETFramework/v4.8/System.Workflow.ComponentModel.xml", "build/.NETFramework/v4.8/System.Workflow.Runtime.dll", "build/.NETFramework/v4.8/System.Workflow.Runtime.xml", "build/.NETFramework/v4.8/System.WorkflowServices.dll", "build/.NETFramework/v4.8/System.WorkflowServices.xml", "build/.NETFramework/v4.8/System.Xaml.dll", "build/.NETFramework/v4.8/System.Xaml.xml", "build/.NETFramework/v4.8/System.Xml.Linq.dll", "build/.NETFramework/v4.8/System.Xml.Linq.xml", "build/.NETFramework/v4.8/System.Xml.Serialization.dll", "build/.NETFramework/v4.8/System.Xml.dll", "build/.NETFramework/v4.8/System.Xml.xml", "build/.NETFramework/v4.8/System.dll", "build/.NETFramework/v4.8/System.xml", "build/.NETFramework/v4.8/UIAutomationClient.dll", "build/.NETFramework/v4.8/UIAutomationClient.xml", "build/.NETFramework/v4.8/UIAutomationClientsideProviders.dll", "build/.NETFramework/v4.8/UIAutomationClientsideProviders.xml", "build/.NETFramework/v4.8/UIAutomationProvider.dll", "build/.NETFramework/v4.8/UIAutomationProvider.xml", "build/.NETFramework/v4.8/UIAutomationTypes.dll", "build/.NETFramework/v4.8/UIAutomationTypes.xml", "build/.NETFramework/v4.8/WindowsBase.dll", "build/.NETFramework/v4.8/WindowsBase.xml", "build/.NETFramework/v4.8/WindowsFormsIntegration.dll", "build/.NETFramework/v4.8/WindowsFormsIntegration.xml", "build/.NETFramework/v4.8/XamlBuildTask.dll", "build/.NETFramework/v4.8/XamlBuildTask.xml", "build/.NETFramework/v4.8/mscorlib.dll", "build/.NETFramework/v4.8/mscorlib.xml", "build/.NETFramework/v4.8/namespaces.xml", "build/.NETFramework/v4.8/sysglobl.dll", "build/.NETFramework/v4.8/sysglobl.xml", "build/Microsoft.NETFramework.ReferenceAssemblies.net48.targets", "microsoft.netframework.referenceassemblies.net48.1.0.3.nupkg.sha512", "microsoft.netframework.referenceassemblies.net48.nuspec"]}, "System.Configuration.ConfigurationManager/8.0.0": {"sha512": "JlYi9XVvIREURRUlGMr1F6vOFLk7YSY4p1vHo4kX3tQ0AGrjqlRWHDi66ImHhy6qwXBG3BJ6Y1QlYQ+Qz6Xgww==", "type": "package", "path": "system.configuration.configurationmanager/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Configuration.ConfigurationManager.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Configuration.ConfigurationManager.targets", "lib/net462/System.Configuration.ConfigurationManager.dll", "lib/net462/System.Configuration.ConfigurationManager.xml", "lib/net6.0/System.Configuration.ConfigurationManager.dll", "lib/net6.0/System.Configuration.ConfigurationManager.xml", "lib/net7.0/System.Configuration.ConfigurationManager.dll", "lib/net7.0/System.Configuration.ConfigurationManager.xml", "lib/net8.0/System.Configuration.ConfigurationManager.dll", "lib/net8.0/System.Configuration.ConfigurationManager.xml", "lib/netstandard2.0/System.Configuration.ConfigurationManager.dll", "lib/netstandard2.0/System.Configuration.ConfigurationManager.xml", "system.configuration.configurationmanager.8.0.0.nupkg.sha512", "system.configuration.configurationmanager.nuspec", "useSharedDesignerContext.txt"]}, "System.Data.SqlClient/4.8.6": {"sha512": "2Ij/LCaTQRyAi5lAv7UUTV9R2FobC8xN9mE0fXBZohum/xLl8IZVmE98Rq5ugQHjCgTBRKqpXRb4ORulRdA6Ig==", "type": "package", "path": "system.data.sqlclient/4.8.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net451/System.Data.SqlClient.dll", "lib/net46/System.Data.SqlClient.dll", "lib/net461/System.Data.SqlClient.dll", "lib/net461/System.Data.SqlClient.xml", "lib/netcoreapp2.1/System.Data.SqlClient.dll", "lib/netcoreapp2.1/System.Data.SqlClient.xml", "lib/netstandard1.2/System.Data.SqlClient.dll", "lib/netstandard1.2/System.Data.SqlClient.xml", "lib/netstandard1.3/System.Data.SqlClient.dll", "lib/netstandard1.3/System.Data.SqlClient.xml", "lib/netstandard2.0/System.Data.SqlClient.dll", "lib/netstandard2.0/System.Data.SqlClient.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net451/System.Data.SqlClient.dll", "ref/net46/System.Data.SqlClient.dll", "ref/net461/System.Data.SqlClient.dll", "ref/net461/System.Data.SqlClient.xml", "ref/netcoreapp2.1/System.Data.SqlClient.dll", "ref/netcoreapp2.1/System.Data.SqlClient.xml", "ref/netstandard1.2/System.Data.SqlClient.dll", "ref/netstandard1.2/System.Data.SqlClient.xml", "ref/netstandard1.2/de/System.Data.SqlClient.xml", "ref/netstandard1.2/es/System.Data.SqlClient.xml", "ref/netstandard1.2/fr/System.Data.SqlClient.xml", "ref/netstandard1.2/it/System.Data.SqlClient.xml", "ref/netstandard1.2/ja/System.Data.SqlClient.xml", "ref/netstandard1.2/ko/System.Data.SqlClient.xml", "ref/netstandard1.2/ru/System.Data.SqlClient.xml", "ref/netstandard1.2/zh-hans/System.Data.SqlClient.xml", "ref/netstandard1.2/zh-hant/System.Data.SqlClient.xml", "ref/netstandard1.3/System.Data.SqlClient.dll", "ref/netstandard1.3/System.Data.SqlClient.xml", "ref/netstandard1.3/de/System.Data.SqlClient.xml", "ref/netstandard1.3/es/System.Data.SqlClient.xml", "ref/netstandard1.3/fr/System.Data.SqlClient.xml", "ref/netstandard1.3/it/System.Data.SqlClient.xml", "ref/netstandard1.3/ja/System.Data.SqlClient.xml", "ref/netstandard1.3/ko/System.Data.SqlClient.xml", "ref/netstandard1.3/ru/System.Data.SqlClient.xml", "ref/netstandard1.3/zh-hans/System.Data.SqlClient.xml", "ref/netstandard1.3/zh-hant/System.Data.SqlClient.xml", "ref/netstandard2.0/System.Data.SqlClient.dll", "ref/netstandard2.0/System.Data.SqlClient.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.dll", "runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.xml", "runtimes/unix/lib/netstandard1.3/System.Data.SqlClient.dll", "runtimes/unix/lib/netstandard2.0/System.Data.SqlClient.dll", "runtimes/unix/lib/netstandard2.0/System.Data.SqlClient.xml", "runtimes/win/lib/net451/System.Data.SqlClient.dll", "runtimes/win/lib/net46/System.Data.SqlClient.dll", "runtimes/win/lib/net461/System.Data.SqlClient.dll", "runtimes/win/lib/net461/System.Data.SqlClient.xml", "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll", "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.xml", "runtimes/win/lib/netstandard1.3/System.Data.SqlClient.dll", "runtimes/win/lib/netstandard2.0/System.Data.SqlClient.dll", "runtimes/win/lib/netstandard2.0/System.Data.SqlClient.xml", "runtimes/win/lib/uap10.0.16299/System.Data.SqlClient.dll", "runtimes/win/lib/uap10.0.16299/System.Data.SqlClient.xml", "system.data.sqlclient.4.8.6.nupkg.sha512", "system.data.sqlclient.nuspec", "useSharedDesignerContext.txt", "version.txt"]}}, "projectFileDependencyGroups": {".NETFramework,Version=v4.8": ["Microsoft.NETFramework.ReferenceAssemblies >= 1.0.3", "System.Configuration.ConfigurationManager >= 8.0.0", "System.Data.SqlClient >= 4.8.6"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\mobli app\\MobileRepairShop.csproj", "projectName": "MobileRepairShop", "projectPath": "C:\\Users\\<USER>\\Desktop\\mobli app\\MobileRepairShop.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\mobli app\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net48"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"targetAlias": "net48", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net48": {"targetAlias": "net48", "dependencies": {"Microsoft.NETFramework.ReferenceAssemblies": {"suppressParent": "All", "target": "Package", "version": "[1.0.3, )", "autoReferenced": true}, "System.Configuration.ConfigurationManager": {"target": "Package", "version": "[8.0.0, )"}, "System.Data.SqlClient": {"target": "Package", "version": "[4.8.6, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.305\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win-x86": {"#import": []}}}}