-- Mobile Phone Repair Shop Database Schema
-- Compatible with SQL Server 2008+ (Windows 7 compatible)

USE master;
GO

-- Create database if it doesn't exist
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'MobileRepairShop')
BEGIN
    CREATE DATABASE MobileRepairShop;
END
GO

USE MobileRepairShop;
GO

-- Create Customers table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Customers' AND xtype='U')
BEGIN
    CREATE TABLE Customers (
        CustomerID int IDENTITY(1,1) PRIMARY KEY,
        FirstName nvarchar(50) NOT NULL,
        LastName nvarchar(50) NOT NULL,
        PhoneNumber nvarchar(20),
        <PERSON>ail nvarchar(100),
        Address nvarchar(255),
        CreatedDate datetime DEFAULT GETDATE(),
        UpdatedDate datetime DEFAULT GETDATE()
    );
END
GO

-- Create Parts table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Parts' AND xtype='U')
BEGIN
    CREATE TABLE Parts (
        PartID int IDENTITY(1,1) PRIMARY KEY,
        PartName nvarchar(100) NOT NULL,
        PartNumber nvarchar(50),
        Description nvarchar(255),
        Category nvarchar(50),
        Brand nvarchar(50),
        Model nvarchar(50),
        CostPrice decimal(10,2) NOT NULL DEFAULT 0,
        SellPrice decimal(10,2) NOT NULL DEFAULT 0,
        StockQuantity int NOT NULL DEFAULT 0,
        MinStockLevel int DEFAULT 5,
        IsActive bit DEFAULT 1,
        CreatedDate datetime DEFAULT GETDATE(),
        UpdatedDate datetime DEFAULT GETDATE()
    );
END
GO

-- Create RepairOrders table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='RepairOrders' AND xtype='U')
BEGIN
    CREATE TABLE RepairOrders (
        OrderID int IDENTITY(1,1) PRIMARY KEY,
        OrderNumber nvarchar(20) UNIQUE NOT NULL,
        CustomerID int NOT NULL,
        PhoneModel nvarchar(100) NOT NULL,
        IMEI nvarchar(20) NOT NULL,
        ProblemDescription nvarchar(500) NOT NULL,
        OrderDate datetime DEFAULT GETDATE(),
        CompletionDate datetime NULL,
        Status nvarchar(20) DEFAULT 'Pending', -- Pending, InProgress, Completed, Delivered, Cancelled
        TotalAmount decimal(10,2) DEFAULT 0,
        LaborCost decimal(10,2) DEFAULT 0,
        Notes nvarchar(500),
        TechnicianName nvarchar(100),
        IsWarranty bit DEFAULT 0,
        WarrantyDays int DEFAULT 30,
        CreatedDate datetime DEFAULT GETDATE(),
        UpdatedDate datetime DEFAULT GETDATE(),
        FOREIGN KEY (CustomerID) REFERENCES Customers(CustomerID)
    );
END
GO

-- Create RepairOrderParts table (junction table for many-to-many relationship)
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='RepairOrderParts' AND xtype='U')
BEGIN
    CREATE TABLE RepairOrderParts (
        OrderPartID int IDENTITY(1,1) PRIMARY KEY,
        OrderID int NOT NULL,
        PartID int NOT NULL,
        Quantity int NOT NULL DEFAULT 1,
        UnitPrice decimal(10,2) NOT NULL,
        TotalPrice AS (Quantity * UnitPrice) PERSISTED,
        IsReplaced bit DEFAULT 1, -- 1 = Replaced, 0 = Repaired
        CreatedDate datetime DEFAULT GETDATE(),
        FOREIGN KEY (OrderID) REFERENCES RepairOrders(OrderID) ON DELETE CASCADE,
        FOREIGN KEY (PartID) REFERENCES Parts(PartID)
    );
END
GO

-- Create indexes for better performance
CREATE NONCLUSTERED INDEX IX_RepairOrders_CustomerID ON RepairOrders(CustomerID);
CREATE NONCLUSTERED INDEX IX_RepairOrders_OrderDate ON RepairOrders(OrderDate);
CREATE NONCLUSTERED INDEX IX_RepairOrders_Status ON RepairOrders(Status);
CREATE NONCLUSTERED INDEX IX_RepairOrderParts_OrderID ON RepairOrderParts(OrderID);
CREATE NONCLUSTERED INDEX IX_RepairOrderParts_PartID ON RepairOrderParts(PartID);
CREATE NONCLUSTERED INDEX IX_Parts_Category ON Parts(Category);
CREATE NONCLUSTERED INDEX IX_Parts_StockQuantity ON Parts(StockQuantity);
GO

-- Insert sample data for testing
INSERT INTO Customers (FirstName, LastName, PhoneNumber, Email, Address) VALUES
('John', 'Doe', '+1234567890', '<EMAIL>', '123 Main St, City'),
('Jane', 'Smith', '+1234567891', '<EMAIL>', '456 Oak Ave, City'),
('Mike', 'Johnson', '+1234567892', '<EMAIL>', '789 Pine Rd, City');

INSERT INTO Parts (PartName, PartNumber, Description, Category, Brand, Model, CostPrice, SellPrice, StockQuantity, MinStockLevel) VALUES
('iPhone 12 Screen', 'IP12-SCR-001', 'Original iPhone 12 LCD Screen Assembly', 'Screen', 'Apple', 'iPhone 12', 45.00, 89.99, 15, 5),
('Samsung S21 Battery', 'SAM-S21-BAT', 'Samsung Galaxy S21 Original Battery', 'Battery', 'Samsung', 'Galaxy S21', 12.50, 24.99, 25, 10),
('iPhone 11 Charging Port', 'IP11-CHG-001', 'iPhone 11 Lightning Charging Port Flex', 'Charging Port', 'Apple', 'iPhone 11', 8.00, 19.99, 20, 8),
('Universal Screen Protector', 'UNIV-PROT-001', 'Tempered Glass Screen Protector', 'Accessory', 'Generic', 'Universal', 2.00, 9.99, 100, 20),
('Phone Opening Tools Kit', 'TOOL-KIT-001', 'Professional Phone Repair Tools Set', 'Tools', 'Generic', 'Universal', 15.00, 29.99, 10, 3);
GO

-- Create stored procedures for common operations
CREATE PROCEDURE sp_GetLowStockParts
AS
BEGIN
    SELECT PartID, PartName, StockQuantity, MinStockLevel
    FROM Parts 
    WHERE StockQuantity <= MinStockLevel AND IsActive = 1
    ORDER BY StockQuantity ASC;
END
GO

CREATE PROCEDURE sp_GetOrderDetails
    @OrderID int
AS
BEGIN
    SELECT 
        ro.OrderID, ro.OrderNumber, ro.OrderDate, ro.Status, ro.TotalAmount,
        ro.PhoneModel, ro.IMEI, ro.ProblemDescription, ro.Notes,
        c.FirstName, c.LastName, c.PhoneNumber, c.Email,
        p.PartName, rop.Quantity, rop.UnitPrice, rop.TotalPrice, rop.IsReplaced
    FROM RepairOrders ro
    INNER JOIN Customers c ON ro.CustomerID = c.CustomerID
    LEFT JOIN RepairOrderParts rop ON ro.OrderID = rop.OrderID
    LEFT JOIN Parts p ON rop.PartID = p.PartID
    WHERE ro.OrderID = @OrderID;
END
GO

CREATE PROCEDURE sp_UpdatePartStock
    @PartID int,
    @QuantityUsed int
AS
BEGIN
    UPDATE Parts 
    SET StockQuantity = StockQuantity - @QuantityUsed,
        UpdatedDate = GETDATE()
    WHERE PartID = @PartID;
END
GO

PRINT 'Database schema created successfully!';
