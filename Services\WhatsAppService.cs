using System;
using System.Diagnostics;
using System.Text;
using System.Web;
using System.Windows.Forms;
using MobileRepairShop.Models;

namespace MobileRepairShop.Services
{
    public class WhatsAppService
    {
        public void SendOrderDetails(RepairOrder order)
        {
            try
            {
                if (order.Customer == null || string.IsNullOrEmpty(order.Customer.PhoneNumber))
                {
                    MessageBox.Show("Customer phone number is required to send WhatsApp message.", 
                        "Missing Phone Number", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var phoneNumber = CleanPhoneNumber(order.Customer.PhoneNumber);
                var message = CreateOrderMessage(order);
                
                // Try WhatsApp Desktop first, then WhatsApp Web
                if (!SendViaWhatsAppDesktop(phoneNumber, message))
                {
                    SendViaWhatsAppWeb(phoneNumber, message);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error sending WhatsApp message: {ex.Message}", 
                    "WhatsApp Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        public void SendOrderStatusUpdate(RepairOrder order)
        {
            try
            {
                if (order.Customer == null || string.IsNullOrEmpty(order.Customer.PhoneNumber))
                {
                    return;
                }

                var phoneNumber = CleanPhoneNumber(order.Customer.PhoneNumber);
                var message = CreateStatusUpdateMessage(order);
                
                if (!SendViaWhatsAppDesktop(phoneNumber, message))
                {
                    SendViaWhatsAppWeb(phoneNumber, message);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error sending status update: {ex.Message}", 
                    "WhatsApp Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        public void SendOrderCompletionNotification(RepairOrder order)
        {
            try
            {
                if (order.Customer == null || string.IsNullOrEmpty(order.Customer.PhoneNumber))
                {
                    return;
                }

                var phoneNumber = CleanPhoneNumber(order.Customer.PhoneNumber);
                var message = CreateCompletionMessage(order);
                
                if (!SendViaWhatsAppDesktop(phoneNumber, message))
                {
                    SendViaWhatsAppWeb(phoneNumber, message);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error sending completion notification: {ex.Message}", 
                    "WhatsApp Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private bool SendViaWhatsAppDesktop(string phoneNumber, string message)
        {
            try
            {
                // Check if WhatsApp Desktop is installed
                var whatsAppPath = GetWhatsAppDesktopPath();
                if (string.IsNullOrEmpty(whatsAppPath))
                {
                    return false;
                }

                // Create WhatsApp URL
                var encodedMessage = HttpUtility.UrlEncode(message);
                var whatsAppUrl = $"whatsapp://send?phone={phoneNumber}&text={encodedMessage}";

                // Launch WhatsApp Desktop
                Process.Start(new ProcessStartInfo
                {
                    FileName = whatsAppUrl,
                    UseShellExecute = true
                });

                MessageBox.Show("WhatsApp Desktop opened. Please send the message manually.", 
                    "WhatsApp", MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                return true;
            }
            catch
            {
                return false;
            }
        }

        private void SendViaWhatsAppWeb(string phoneNumber, string message)
        {
            try
            {
                var encodedMessage = HttpUtility.UrlEncode(message);
                var whatsAppWebUrl = $"https://web.whatsapp.com/send?phone={phoneNumber}&text={encodedMessage}";

                // Open WhatsApp Web in default browser
                Process.Start(new ProcessStartInfo
                {
                    FileName = whatsAppWebUrl,
                    UseShellExecute = true
                });

                MessageBox.Show("WhatsApp Web opened in your browser. Please send the message manually.", 
                    "WhatsApp Web", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening WhatsApp Web: {ex.Message}", 
                    "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private string GetWhatsAppDesktopPath()
        {
            try
            {
                // Common WhatsApp Desktop installation paths
                var possiblePaths = new[]
                {
                    @"C:\Users\<USER>\AppData\Local\WhatsApp\WhatsApp.exe",
                    @"C:\Program Files\WhatsApp\WhatsApp.exe",
                    @"C:\Program Files (x86)\WhatsApp\WhatsApp.exe"
                };

                foreach (var path in possiblePaths)
                {
                    if (System.IO.File.Exists(path))
                    {
                        return path;
                    }
                }

                return null;
            }
            catch
            {
                return null;
            }
        }

        private string CleanPhoneNumber(string phoneNumber)
        {
            // Remove all non-digit characters
            var cleaned = System.Text.RegularExpressions.Regex.Replace(phoneNumber, @"[^\d]", "");
            
            // Add country code if not present (assuming US +1 if no country code)
            if (cleaned.Length == 10)
            {
                cleaned = "1" + cleaned;
            }
            
            return cleaned;
        }

        private string CreateOrderMessage(RepairOrder order)
        {
            var sb = new StringBuilder();
            
            sb.AppendLine("🔧 *Mobile Repair Shop*");
            sb.AppendLine("📱 *Repair Order Confirmation*");
            sb.AppendLine();
            sb.AppendLine($"*Order Number:* {order.OrderNumber}");
            sb.AppendLine($"*Date:* {order.OrderDate:MM/dd/yyyy}");
            sb.AppendLine($"*Customer:* {order.Customer?.FullName}");
            sb.AppendLine();
            sb.AppendLine("📱 *Device Information:*");
            sb.AppendLine($"*Model:* {order.PhoneModel}");
            sb.AppendLine($"*IMEI:* {order.IMEI}");
            sb.AppendLine();
            sb.AppendLine("🔍 *Problem Description:*");
            sb.AppendLine(order.ProblemDescription);
            sb.AppendLine();
            sb.AppendLine($"*Status:* {order.Status}");
            sb.AppendLine($"*Estimated Cost:* {order.TotalAmount:C}");
            
            if (!string.IsNullOrEmpty(order.TechnicianName))
            {
                sb.AppendLine($"*Technician:* {order.TechnicianName}");
            }
            
            if (order.IsWarranty)
            {
                sb.AppendLine();
                sb.AppendLine($"✅ *Warranty:* {order.WarrantyDays} days");
            }
            
            sb.AppendLine();
            sb.AppendLine("We will keep you updated on the progress of your repair.");
            sb.AppendLine("Thank you for choosing our service! 🙏");

            return sb.ToString();
        }

        private string CreateStatusUpdateMessage(RepairOrder order)
        {
            var sb = new StringBuilder();
            
            sb.AppendLine("🔧 *Mobile Repair Shop*");
            sb.AppendLine("📱 *Repair Status Update*");
            sb.AppendLine();
            sb.AppendLine($"*Order Number:* {order.OrderNumber}");
            sb.AppendLine($"*Device:* {order.PhoneModel}");
            sb.AppendLine();
            
            switch (order.Status)
            {
                case OrderStatus.Pending:
                    sb.AppendLine("⏳ *Status:* Pending - Your device is in queue for diagnosis");
                    break;
                case OrderStatus.InProgress:
                    sb.AppendLine("🔧 *Status:* In Progress - Our technician is working on your device");
                    break;
                case OrderStatus.Completed:
                    sb.AppendLine("✅ *Status:* Completed - Your device is ready for pickup!");
                    break;
                case OrderStatus.Delivered:
                    sb.AppendLine("📦 *Status:* Delivered - Thank you for your business!");
                    break;
                case OrderStatus.Cancelled:
                    sb.AppendLine("❌ *Status:* Cancelled");
                    break;
            }
            
            if (!string.IsNullOrEmpty(order.Notes))
            {
                sb.AppendLine();
                sb.AppendLine("📝 *Notes:*");
                sb.AppendLine(order.Notes);
            }
            
            sb.AppendLine();
            sb.AppendLine("If you have any questions, please don't hesitate to contact us.");

            return sb.ToString();
        }

        private string CreateCompletionMessage(RepairOrder order)
        {
            var sb = new StringBuilder();
            
            sb.AppendLine("🎉 *Mobile Repair Shop*");
            sb.AppendLine("✅ *Repair Completed!*");
            sb.AppendLine();
            sb.AppendLine($"*Order Number:* {order.OrderNumber}");
            sb.AppendLine($"*Device:* {order.PhoneModel}");
            sb.AppendLine($"*Completion Date:* {order.CompletionDate?.ToString("MM/dd/yyyy") ?? DateTime.Now.ToString("MM/dd/yyyy")}");
            sb.AppendLine();
            sb.AppendLine("🔧 *Work Performed:*");
            
            if (order.OrderParts.Count > 0)
            {
                foreach (var part in order.OrderParts)
                {
                    var action = part.IsReplaced ? "Replaced" : "Repaired";
                    sb.AppendLine($"• {action}: {part.Part?.PartName} (Qty: {part.Quantity})");
                }
            }
            
            if (order.LaborCost > 0)
            {
                sb.AppendLine("• Labor and diagnostic services");
            }
            
            sb.AppendLine();
            sb.AppendLine($"💰 *Total Cost:* {order.TotalAmount:C}");
            
            if (order.IsWarranty)
            {
                sb.AppendLine();
                sb.AppendLine($"🛡️ *Warranty:* {order.WarrantyDays} days from completion date");
                sb.AppendLine("Keep this message as proof of warranty coverage.");
            }
            
            sb.AppendLine();
            sb.AppendLine("📍 *Your device is ready for pickup!*");
            sb.AppendLine("Please bring a valid ID when collecting your device.");
            sb.AppendLine();
            sb.AppendLine("Thank you for trusting us with your device repair! 🙏");
            sb.AppendLine("We appreciate your business and look forward to serving you again.");

            return sb.ToString();
        }
    }
}
