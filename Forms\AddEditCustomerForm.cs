using System;
using System.Drawing;
using System.Windows.Forms;
using MobileRepairShop.DataAccess;
using MobileRepairShop.Models;

namespace MobileRepairShop.Forms
{
    public partial class AddEditCustomerForm : Form
    {
        private readonly CustomerRepository _customerRepository;
        private readonly Customer _customer;
        private readonly bool _isEditMode;

        private TextBox txtFirstName;
        private TextBox txtLastName;
        private TextBox txtPhoneNumber;
        private TextBox txtEmail;
        private TextBox txtAddress;
        private Button btnSave;
        private Button btnCancel;

        public AddEditCustomerForm(Customer customer = null)
        {
            _customerRepository = new CustomerRepository();
            _customer = customer ?? new Customer();
            _isEditMode = customer != null;
            
            InitializeComponent();
            SetupForm();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // AddEditCustomerForm
            this.AutoScaleDimensions = new SizeF(8F, 16F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.BackColor = Color.FromArgb(240, 240, 240);
            this.ClientSize = new Size(450, 400);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "AddEditCustomerForm";
            this.StartPosition = FormStartPosition.CenterParent;
            this.Text = _isEditMode ? "Edit Customer" : "Add New Customer";
            
            this.ResumeLayout(false);
        }

        private void SetupForm()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20),
                BackColor = Color.White
            };

            // Title
            var titleLabel = new Label
            {
                Text = _isEditMode ? "Edit Customer" : "Add New Customer",
                Font = new Font("Segoe UI", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(51, 51, 76),
                AutoSize = true,
                Location = new Point(20, 20)
            };

            // First Name
            var lblFirstName = CreateLabel("First Name:", 70);
            txtFirstName = CreateTextBox(100);

            // Last Name
            var lblLastName = CreateLabel("Last Name:", 130);
            txtLastName = CreateTextBox(160);

            // Phone Number
            var lblPhoneNumber = CreateLabel("Phone Number:", 190);
            txtPhoneNumber = CreateTextBox(220);

            // Email
            var lblEmail = CreateLabel("Email:", 250);
            txtEmail = CreateTextBox(280);

            // Address
            var lblAddress = CreateLabel("Address:", 310);
            txtAddress = new TextBox
            {
                Location = new Point(20, 340),
                Size = new Size(350, 25),
                Font = new Font("Segoe UI", 10)
            };

            // Buttons
            btnSave = new Button
            {
                Text = "Save",
                Location = new Point(220, 380),
                Size = new Size(80, 35),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10),
                Cursor = Cursors.Hand,
                FlatAppearance = { BorderSize = 0 }
            };
            btnSave.Click += BtnSave_Click;

            btnCancel = new Button
            {
                Text = "Cancel",
                Location = new Point(320, 380),
                Size = new Size(80, 35),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10),
                Cursor = Cursors.Hand,
                FlatAppearance = { BorderSize = 0 }
            };
            btnCancel.Click += (s, e) => this.DialogResult = DialogResult.Cancel;

            // Add controls to panel
            panel.Controls.AddRange(new Control[]
            {
                titleLabel, lblFirstName, txtFirstName, lblLastName, txtLastName,
                lblPhoneNumber, txtPhoneNumber, lblEmail, txtEmail,
                lblAddress, txtAddress, btnSave, btnCancel
            });

            this.Controls.Add(panel);
        }

        private Label CreateLabel(string text, int y)
        {
            return new Label
            {
                Text = text,
                Location = new Point(20, y),
                AutoSize = true,
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(51, 51, 76)
            };
        }

        private TextBox CreateTextBox(int y)
        {
            return new TextBox
            {
                Location = new Point(20, y),
                Size = new Size(200, 25),
                Font = new Font("Segoe UI", 10)
            };
        }

        private void LoadData()
        {
            if (_isEditMode)
            {
                txtFirstName.Text = _customer.FirstName;
                txtLastName.Text = _customer.LastName;
                txtPhoneNumber.Text = _customer.PhoneNumber;
                txtEmail.Text = _customer.Email;
                txtAddress.Text = _customer.Address;
            }
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (ValidateInput())
            {
                try
                {
                    _customer.FirstName = txtFirstName.Text.Trim();
                    _customer.LastName = txtLastName.Text.Trim();
                    _customer.PhoneNumber = txtPhoneNumber.Text.Trim();
                    _customer.Email = txtEmail.Text.Trim();
                    _customer.Address = txtAddress.Text.Trim();

                    if (_isEditMode)
                    {
                        _customerRepository.UpdateCustomer(_customer);
                        MessageBox.Show("Customer updated successfully.", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        _customerRepository.AddCustomer(_customer);
                        MessageBox.Show("Customer added successfully.", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }

                    this.DialogResult = DialogResult.OK;
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error saving customer: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtFirstName.Text))
            {
                MessageBox.Show("First name is required.", "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtFirstName.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtLastName.Text))
            {
                MessageBox.Show("Last name is required.", "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtLastName.Focus();
                return false;
            }

            return true;
        }
    }
}
