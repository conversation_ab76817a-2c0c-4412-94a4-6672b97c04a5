using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using MobileRepairShop.Models;

namespace MobileRepairShop.DataAccess
{
    public class PartsRepository
    {
        public List<Part> GetAllParts()
        {
            return MockDataService.GetAllParts();
        }

        public Part GetPartById(int partId)
        {
            return MockDataService.GetPartById(partId);
        }

        public List<Part> GetLowStockParts()
        {
            var parts = new List<Part>();
            string sql = @"SELECT PartID, PartName, PartNumber, Description, Category, Brand, Model, 
                          CostPrice, SellPrice, StockQuantity, MinStockLevel, IsActive, CreatedDate, UpdatedDate 
                          FROM Parts WHERE StockQuantity <= MinStockLevel AND IsActive = 1 ORDER BY StockQuantity";

            using (var connection = DatabaseConnection.GetConnection())
            {
                connection.Open();
                using (var reader = DatabaseConnection.ExecuteReader(sql, connection))
                {
                    while (reader.Read())
                    {
                        parts.Add(MapReaderToPart(reader));
                    }
                }
            }
            return parts;
        }

        public List<Part> SearchParts(string searchTerm)
        {
            var parts = new List<Part>();
            string sql = @"SELECT PartID, PartName, PartNumber, Description, Category, Brand, Model, 
                          CostPrice, SellPrice, StockQuantity, MinStockLevel, IsActive, CreatedDate, UpdatedDate 
                          FROM Parts 
                          WHERE (PartName LIKE @SearchTerm OR PartNumber LIKE @SearchTerm OR Brand LIKE @SearchTerm OR Model LIKE @SearchTerm) 
                          AND IsActive = 1 ORDER BY PartName";

            using (var connection = DatabaseConnection.GetConnection())
            {
                connection.Open();
                using (var reader = DatabaseConnection.ExecuteReader(sql, connection, new SqlParameter("@SearchTerm", $"%{searchTerm}%")))
                {
                    while (reader.Read())
                    {
                        parts.Add(MapReaderToPart(reader));
                    }
                }
            }
            return parts;
        }

        public int AddPart(Part part)
        {
            string sql = @"INSERT INTO Parts (PartName, PartNumber, Description, Category, Brand, Model, 
                          CostPrice, SellPrice, StockQuantity, MinStockLevel, IsActive, CreatedDate, UpdatedDate) 
                          VALUES (@PartName, @PartNumber, @Description, @Category, @Brand, @Model, 
                          @CostPrice, @SellPrice, @StockQuantity, @MinStockLevel, @IsActive, @CreatedDate, @UpdatedDate);
                          SELECT SCOPE_IDENTITY();";

            var parameters = new[]
            {
                new SqlParameter("@PartName", part.PartName ?? ""),
                new SqlParameter("@PartNumber", part.PartNumber ?? ""),
                new SqlParameter("@Description", part.Description ?? ""),
                new SqlParameter("@Category", part.Category ?? ""),
                new SqlParameter("@Brand", part.Brand ?? ""),
                new SqlParameter("@Model", part.Model ?? ""),
                new SqlParameter("@CostPrice", part.CostPrice),
                new SqlParameter("@SellPrice", part.SellPrice),
                new SqlParameter("@StockQuantity", part.StockQuantity),
                new SqlParameter("@MinStockLevel", part.MinStockLevel),
                new SqlParameter("@IsActive", part.IsActive),
                new SqlParameter("@CreatedDate", part.CreatedDate),
                new SqlParameter("@UpdatedDate", part.UpdatedDate)
            };

            var result = DatabaseConnection.ExecuteScalar(sql, parameters);
            return Convert.ToInt32(result);
        }

        public void UpdatePart(Part part)
        {
            string sql = @"UPDATE Parts SET 
                          PartName = @PartName, PartNumber = @PartNumber, Description = @Description, 
                          Category = @Category, Brand = @Brand, Model = @Model, CostPrice = @CostPrice, 
                          SellPrice = @SellPrice, StockQuantity = @StockQuantity, MinStockLevel = @MinStockLevel, 
                          IsActive = @IsActive, UpdatedDate = @UpdatedDate 
                          WHERE PartID = @PartID";

            var parameters = new[]
            {
                new SqlParameter("@PartID", part.PartID),
                new SqlParameter("@PartName", part.PartName ?? ""),
                new SqlParameter("@PartNumber", part.PartNumber ?? ""),
                new SqlParameter("@Description", part.Description ?? ""),
                new SqlParameter("@Category", part.Category ?? ""),
                new SqlParameter("@Brand", part.Brand ?? ""),
                new SqlParameter("@Model", part.Model ?? ""),
                new SqlParameter("@CostPrice", part.CostPrice),
                new SqlParameter("@SellPrice", part.SellPrice),
                new SqlParameter("@StockQuantity", part.StockQuantity),
                new SqlParameter("@MinStockLevel", part.MinStockLevel),
                new SqlParameter("@IsActive", part.IsActive),
                new SqlParameter("@UpdatedDate", DateTime.Now)
            };

            DatabaseConnection.ExecuteNonQuery(sql, parameters);
        }

        public void UpdatePartStock(int partId, int quantityUsed)
        {
            string sql = "UPDATE Parts SET StockQuantity = StockQuantity - @QuantityUsed, UpdatedDate = @UpdatedDate WHERE PartID = @PartID";
            var parameters = new[]
            {
                new SqlParameter("@PartID", partId),
                new SqlParameter("@QuantityUsed", quantityUsed),
                new SqlParameter("@UpdatedDate", DateTime.Now)
            };
            DatabaseConnection.ExecuteNonQuery(sql, parameters);
        }

        public void DeletePart(int partId)
        {
            string sql = "UPDATE Parts SET IsActive = 0, UpdatedDate = @UpdatedDate WHERE PartID = @PartID";
            var parameters = new[]
            {
                new SqlParameter("@PartID", partId),
                new SqlParameter("@UpdatedDate", DateTime.Now)
            };
            DatabaseConnection.ExecuteNonQuery(sql, parameters);
        }

        private Part MapReaderToPart(SqlDataReader reader)
        {
            return new Part
            {
                PartID = reader.GetInt32("PartID"),
                PartName = reader.IsDBNull(reader.GetOrdinal("PartName")) ? "" : reader.GetString("PartName"),
                PartNumber = reader.IsDBNull(reader.GetOrdinal("PartNumber")) ? "" : reader.GetString("PartNumber"),
                Description = reader.IsDBNull(reader.GetOrdinal("Description")) ? "" : reader.GetString("Description"),
                Category = reader.IsDBNull(reader.GetOrdinal("Category")) ? "" : reader.GetString("Category"),
                Brand = reader.IsDBNull(reader.GetOrdinal("Brand")) ? "" : reader.GetString("Brand"),
                Model = reader.IsDBNull(reader.GetOrdinal("Model")) ? "" : reader.GetString("Model"),
                CostPrice = reader.GetDecimal("CostPrice"),
                SellPrice = reader.GetDecimal("SellPrice"),
                StockQuantity = reader.GetInt32("StockQuantity"),
                MinStockLevel = reader.GetInt32("MinStockLevel"),
                IsActive = reader.GetBoolean("IsActive"),
                CreatedDate = reader.GetDateTime("CreatedDate"),
                UpdatedDate = reader.GetDateTime("UpdatedDate")
            };
        }
    }
}
