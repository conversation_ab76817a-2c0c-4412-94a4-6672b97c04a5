using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using MobileRepairShop.Models;

namespace MobileRepairShop.DataAccess
{
    public class PartsRepository
    {
        public List<Part> GetAllParts()
        {
            return MockDataService.GetAllParts();
        }

        public Part GetPartById(int partId)
        {
            return MockDataService.GetPartById(partId);
        }

        public List<Part> GetLowStockParts()
        {
            return MockDataService.GetLowStockParts();
        }

        public List<Part> SearchParts(string searchTerm)
        {
            return MockDataService.SearchParts(searchTerm);
        }

        public int AddPart(Part part)
        {
            return MockDataService.AddPart(part);
        }

        public void UpdatePart(Part part)
        {
            MockDataService.UpdatePart(part);
        }

        public void UpdatePartStock(int partId, int quantityUsed)
        {
            MockDataService.UpdatePartStock(partId, quantityUsed);
        }

        public void DeletePart(int partId)
        {
            MockDataService.DeletePart(partId);
        }


    }
}
