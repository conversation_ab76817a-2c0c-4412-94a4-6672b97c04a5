# Mobile Repair Shop Management System

A complete C# Windows Forms application for managing mobile phone repair shops, featuring inventory management, repair order tracking, thermal printing, and WhatsApp integration.

## Features

### Core Functionality
- **Dashboard**: Overview of orders, revenue, and low stock alerts
- **Parts Management**: Complete inventory tracking with stock levels
- **Repair Orders**: Full order lifecycle management
- **Customer Management**: Customer database with contact information
- **Reports & Analytics**: Comprehensive reporting with export capabilities

### Advanced Features
- **Thermal Printing**: Print stickers and receipts on USB thermal printers
- **WhatsApp Integration**: Send order updates via WhatsApp Desktop/Web
- **Modern UI**: Clean, professional interface with color-coded status indicators
- **Database Integration**: SQL Server backend with ADO.NET data access
- **Windows 7+ Compatible**: Built with .NET Framework 4.6.2

## System Requirements

### Minimum Requirements
- **Operating System**: Windows 7 SP1 or later
- **.NET Framework**: 4.6.2 or later
- **Database**: SQL Server 2008 Express or later (SQL Server LocalDB supported)
- **Memory**: 2 GB RAM minimum, 4 GB recommended
- **Storage**: 100 MB free disk space

### Optional Components
- **Thermal Printer**: Any ESC/POS compatible USB thermal printer
- **WhatsApp**: WhatsApp Desktop application or web browser for WhatsApp Web

## Installation Instructions

### 1. Database Setup

1. **Install SQL Server Express** (if not already installed):
   - Download from Microsoft's official website
   - Choose SQL Server Express with LocalDB for simple setup

2. **Create the Database**:
   - Open SQL Server Management Studio (SSMS) or use sqlcmd
   - Run the script from `Database/CreateDatabase.sql`
   - This will create the `MobileRepairShop` database with all required tables

3. **Configure Connection String**:
   - Edit `App.config` file
   - Update the connection string to match your SQL Server instance:
   ```xml
   <connectionStrings>
     <add name="MobileRepairShop" 
          connectionString="Data Source=.\SQLEXPRESS;Initial Catalog=MobileRepairShop;Integrated Security=True" 
          providerName="System.Data.SqlClient" />
   </connectionStrings>
   ```

### 2. Application Setup

1. **Build the Application**:
   - Open the project in Visual Studio 2017 or later
   - Build the solution (Build → Build Solution)
   - The executable will be created in `bin/Release/` folder

2. **Deploy to Target Machine**:
   - Copy the entire `bin/Release/` folder to the target machine
   - Ensure .NET Framework 4.6.2 is installed on the target machine
   - Run `MobileRepairShop.exe`

### 3. Configuration

1. **Company Information**:
   - Edit `App.config` to update company details:
   ```xml
   <appSettings>
     <add key="CompanyName" value="Your Repair Shop Name" />
     <add key="CompanyAddress" value="Your Address" />
     <add key="CompanyPhone" value="Your Phone Number" />
     <add key="CompanyEmail" value="Your Email" />
   </appSettings>
   ```

2. **Printer Setup**:
   - Install thermal printer drivers
   - Update printer names in `App.config`:
   ```xml
   <add key="ThermalPrinterName" value="Your Thermal Printer Name" />
   <add key="ReceiptPrinterName" value="Your Receipt Printer Name" />
   ```

## Usage Guide

### Getting Started

1. **First Launch**:
   - The application will test the database connection on startup
   - If successful, you'll see the main dashboard
   - Sample data is automatically created for testing

2. **Adding Parts**:
   - Navigate to Parts Management
   - Click "Add New Part"
   - Fill in part details including stock levels
   - Set minimum stock levels for low stock alerts

3. **Creating Customers**:
   - Go to Customer Management
   - Add customer information including phone numbers for WhatsApp
   - Customers can be added directly from the new order form

4. **Processing Orders**:
   - Click "New Order" from the Orders section
   - Select customer and enter device details
   - Add parts used in the repair
   - Set labor costs and technician
   - Save the order

### Printing Features

#### Sticker Printing
- Small labels for device identification
- Contains order number, customer, device model, IMEI, and status
- Ideal for sticking on the back of devices

#### Receipt Printing
- Full 80mm receipts for customers
- Includes company information, order details, parts used, and warranty info
- Professional format suitable for business records

### WhatsApp Integration

1. **Setup**:
   - Install WhatsApp Desktop or ensure web browser access
   - Customer phone numbers must be in international format

2. **Sending Messages**:
   - Select an order and click "Send WhatsApp"
   - Application opens WhatsApp with pre-formatted message
   - Manually send the message from WhatsApp

3. **Message Types**:
   - Order confirmation
   - Status updates
   - Completion notifications

### Reports and Analytics

Available reports include:
- **Sales Summary**: Daily sales overview
- **Order Details**: Complete order listings
- **Parts Usage**: Most used parts analysis
- **Customer Report**: Customer activity summary
- **Revenue Analysis**: Monthly revenue breakdown
- **Low Stock Report**: Parts needing restock
- **Technician Performance**: Individual technician metrics

## Database Schema

### Tables Overview

- **Customers**: Customer information and contact details
- **Parts**: Inventory items with pricing and stock levels
- **RepairOrders**: Main order records with status tracking
- **RepairOrderParts**: Junction table linking orders to parts used

### Key Features

- **Referential Integrity**: Foreign key constraints ensure data consistency
- **Audit Trail**: Created/Updated timestamps on all records
- **Stock Management**: Automatic stock deduction when parts are used
- **Flexible Status**: Configurable order status workflow

## Troubleshooting

### Common Issues

1. **Database Connection Errors**:
   - Verify SQL Server is running
   - Check connection string in App.config
   - Ensure Windows Authentication or SQL Authentication is properly configured

2. **Printing Issues**:
   - Verify printer drivers are installed
   - Check printer names in App.config match system printer names
   - Test printing from other applications first

3. **WhatsApp Not Opening**:
   - Ensure WhatsApp Desktop is installed
   - Check if default browser supports WhatsApp Web
   - Verify phone numbers are in correct international format

### Performance Optimization

- **Regular Database Maintenance**: Run SQL Server maintenance plans
- **Archive Old Orders**: Move completed orders older than 1 year to archive tables
- **Index Optimization**: Monitor query performance and add indexes as needed

## Security Considerations

- **Database Security**: Use SQL Server authentication in production environments
- **User Access**: Implement Windows user groups for role-based access
- **Data Backup**: Regular automated backups of the database
- **Network Security**: Use encrypted connections for remote database access

## Support and Maintenance

### Regular Maintenance Tasks

1. **Weekly**:
   - Review low stock reports
   - Check for completed orders needing status updates
   - Backup database

2. **Monthly**:
   - Generate revenue reports
   - Review customer activity
   - Update part pricing as needed

3. **Quarterly**:
   - Archive old completed orders
   - Review and optimize database performance
   - Update application if new versions available

### Backup Procedures

1. **Database Backup**:
   ```sql
   BACKUP DATABASE MobileRepairShop 
   TO DISK = 'C:\Backups\MobileRepairShop_YYYYMMDD.bak'
   ```

2. **Application Backup**:
   - Copy entire application folder
   - Include App.config with current settings
   - Document any customizations made

## License and Support

This application is provided as-is for educational and commercial use. For technical support or customization requests, please contact the development team.

---

**Version**: 1.0.0  
**Last Updated**: 2024  
**Compatibility**: Windows 7+ with .NET Framework 4.6.2+
