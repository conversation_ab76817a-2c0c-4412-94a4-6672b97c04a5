using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using MobileRepairShop.DataAccess;
using MobileRepairShop.Models;

namespace MobileRepairShop.Forms
{
    public partial class DashboardForm : Form
    {
        private readonly RepairOrderRepository _orderRepository;
        private readonly PartsRepository _partsRepository;
        private readonly CustomerRepository _customerRepository;

        private Panel statsPanel;
        private Panel recentOrdersPanel;
        private Panel lowStockPanel;
        private Label lblTotalOrders;
        private Label lblPendingOrders;
        private Label lblCompletedOrders;
        private Label lblTotalRevenue;
        private DataGridView dgvRecentOrders;
        private DataGridView dgvLowStock;

        public DashboardForm()
        {
            _orderRepository = new RepairOrderRepository();
            _partsRepository = new PartsRepository();
            _customerRepository = new CustomerRepository();
            
            InitializeComponent();
            SetupDashboard();
            LoadDashboardData();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // DashboardForm
            this.AutoScaleDimensions = new SizeF(8F, 16F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.BackColor = Color.FromArgb(240, 240, 240);
            this.ClientSize = new Size(1000, 700);
            this.Name = "DashboardForm";
            this.Text = "Dashboard";
            
            this.ResumeLayout(false);
        }

        private void SetupDashboard()
        {
            // Title
            var titleLabel = new Label
            {
                Text = "Dashboard",
                Font = new Font("Segoe UI", 24, FontStyle.Bold),
                ForeColor = Color.FromArgb(51, 51, 76),
                AutoSize = true,
                Location = new Point(20, 20)
            };

            // Stats Panel
            statsPanel = new Panel
            {
                Location = new Point(20, 70),
                Size = new Size(960, 120),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            CreateStatsCards();

            // Recent Orders Panel
            recentOrdersPanel = new Panel
            {
                Location = new Point(20, 210),
                Size = new Size(470, 400),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var recentOrdersTitle = new Label
            {
                Text = "Recent Orders",
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(51, 51, 76),
                Location = new Point(10, 10),
                AutoSize = true
            };

            dgvRecentOrders = new DataGridView
            {
                Location = new Point(10, 40),
                Size = new Size(450, 350),
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };

            recentOrdersPanel.Controls.Add(recentOrdersTitle);
            recentOrdersPanel.Controls.Add(dgvRecentOrders);

            // Low Stock Panel
            lowStockPanel = new Panel
            {
                Location = new Point(510, 210),
                Size = new Size(470, 400),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var lowStockTitle = new Label
            {
                Text = "Low Stock Alert",
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(220, 53, 69),
                Location = new Point(10, 10),
                AutoSize = true
            };

            dgvLowStock = new DataGridView
            {
                Location = new Point(10, 40),
                Size = new Size(450, 350),
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };

            lowStockPanel.Controls.Add(lowStockTitle);
            lowStockPanel.Controls.Add(dgvLowStock);

            // Add all panels to form
            this.Controls.Add(titleLabel);
            this.Controls.Add(statsPanel);
            this.Controls.Add(recentOrdersPanel);
            this.Controls.Add(lowStockPanel);
        }

        private void CreateStatsCards()
        {
            // Total Orders Card
            var totalOrdersCard = CreateStatsCard("Total Orders", "0", Color.FromArgb(0, 126, 249), 20);
            lblTotalOrders = totalOrdersCard.Controls.OfType<Label>().FirstOrDefault(l => l.Name == "value");

            // Pending Orders Card
            var pendingOrdersCard = CreateStatsCard("Pending", "0", Color.FromArgb(255, 193, 7), 250);
            lblPendingOrders = pendingOrdersCard.Controls.OfType<Label>().FirstOrDefault(l => l.Name == "value");

            // Completed Orders Card
            var completedOrdersCard = CreateStatsCard("Completed", "0", Color.FromArgb(40, 167, 69), 480);
            lblCompletedOrders = completedOrdersCard.Controls.OfType<Label>().FirstOrDefault(l => l.Name == "value");

            // Total Revenue Card
            var revenueCard = CreateStatsCard("Revenue", "$0", Color.FromArgb(108, 117, 125), 710);
            lblTotalRevenue = revenueCard.Controls.OfType<Label>().FirstOrDefault(l => l.Name == "value");

            statsPanel.Controls.Add(totalOrdersCard);
            statsPanel.Controls.Add(pendingOrdersCard);
            statsPanel.Controls.Add(completedOrdersCard);
            statsPanel.Controls.Add(revenueCard);
        }

        private Panel CreateStatsCard(string title, string value, Color color, int x)
        {
            var card = new Panel
            {
                Location = new Point(x, 10),
                Size = new Size(200, 100),
                BackColor = color,
                BorderStyle = BorderStyle.FixedSingle
            };

            var titleLabel = new Label
            {
                Text = title,
                Font = new Font("Segoe UI", 10, FontStyle.Regular),
                ForeColor = Color.White,
                Location = new Point(10, 10),
                AutoSize = true
            };

            var valueLabel = new Label
            {
                Text = value,
                Name = "value",
                Font = new Font("Segoe UI", 20, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(10, 35),
                AutoSize = true
            };

            card.Controls.Add(titleLabel);
            card.Controls.Add(valueLabel);

            return card;
        }

        private void LoadDashboardData()
        {
            try
            {
                // Load statistics
                var allOrders = _orderRepository.GetAllOrders();
                var pendingOrders = allOrders.Where(o => o.Status == OrderStatus.Pending).Count();
                var completedOrders = allOrders.Where(o => o.Status == OrderStatus.Completed).Count();
                var totalRevenue = allOrders.Where(o => o.Status == OrderStatus.Completed).Sum(o => o.TotalAmount);

                lblTotalOrders.Text = allOrders.Count.ToString();
                lblPendingOrders.Text = pendingOrders.ToString();
                lblCompletedOrders.Text = completedOrders.ToString();
                lblTotalRevenue.Text = $"${totalRevenue:F2}";

                // Load recent orders
                var recentOrders = allOrders.Take(10).Select(o => new
                {
                    OrderNumber = o.OrderNumber,
                    Customer = o.Customer?.FullName ?? "Unknown",
                    PhoneModel = o.PhoneModel,
                    Status = o.Status.ToString(),
                    Amount = o.TotalAmount.ToString("C"),
                    Date = o.OrderDate.ToString("MM/dd/yyyy")
                }).ToList();

                dgvRecentOrders.DataSource = recentOrders;

                // Load low stock parts
                var lowStockParts = _partsRepository.GetLowStockParts().Select(p => new
                {
                    PartName = p.PartName,
                    Brand = p.Brand,
                    Model = p.Model,
                    Stock = p.StockQuantity,
                    MinLevel = p.MinStockLevel
                }).ToList();

                dgvLowStock.DataSource = lowStockParts;

                // Style the data grids
                StyleDataGridView(dgvRecentOrders);
                StyleDataGridView(dgvLowStock);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading dashboard data: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void StyleDataGridView(DataGridView dgv)
        {
            dgv.EnableHeadersVisualStyles = false;
            dgv.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(51, 51, 76);
            dgv.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgv.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 9, FontStyle.Bold);
            dgv.DefaultCellStyle.SelectionBackColor = Color.FromArgb(0, 126, 249);
            dgv.DefaultCellStyle.SelectionForeColor = Color.White;
            dgv.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);
        }
    }
}
